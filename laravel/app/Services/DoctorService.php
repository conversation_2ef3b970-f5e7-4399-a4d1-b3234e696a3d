<?php


namespace App\Services;

use App\Account;
use App\ActualVisit;
use App\Doctor;
use App\Exceptions\CrmException;
use App\Models\ListType;
use App\Models\OtherSetting;
use App\PlanVisit;
use App\User;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DoctorService
{


    public function getDoctors(?array $lines = [], ?array $divisions = [], ?Carbon $from = null, ?Carbon $to = null, ?array $specialities = [], ?array $accountTypes = [], ?array $doctor_ids = [], ?array $bricks = [], ?int $shift_id = null): Collection
    {
        $setting = ListType::first()->type == 'Default List';
        $doctors = Account::select(
            [
                'doctors.id as doctor_id',
                'doctors.id as id',
                DB::raw('IFNULL(group_concat(distinct crm_accounts.name),"") as account'),
                DB::raw('IFNULL(group_concat(distinct crm_accounts.id),"") as account_id'),
                DB::raw('IFNULL(group_concat(distinct crm_accounts.code),"") as acc_code'),
                DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
                // DB::raw('IFNULL(group_concat(distinct crm_line_divisions.name),"") as division'),
                DB::raw('IFNULL(group_concat(distinct crm_users.fullname),"") as employee'),
                // DB::raw('IFNULL(group_concat(distinct crm_lines.name),"") as line'),
                DB::raw('IFNULL(group_concat(distinct crm_account_types.name),"") as account_type'),
                DB::raw('IFNULL(group_concat(distinct crm_account_types.shift_id),"") as acc_shift_id'),
                DB::raw('IFNULL(group_concat(distinct crm_account_types.id),"") as account_type_id'),
                DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
                // DB::raw('IFNULL(group_concat(distinct crm_lines.name),"") as line'),
                // DB::raw('IFNULL(group_concat(distinct crm_line_divisions.name),"") as division'),
                'lines.name as line',
                'lines.id as line_id',
                'line_divisions.name as division',
                'line_divisions.id as div_id',
                'doctors.name as doctor',
                'doctors.ucode as ucode',
                DB::raw('IFNULL(group_concat(distinct crm_d.name),"") as class'),
                DB::raw('IFNULL(group_concat(distinct crm_d.id),"") as doc_class_id'),
                'specialities.name as speciality',
                'specialities.id as speciality_id',
                DB::raw("DATE_FORMAT(crm_doctors.active_date,'%Y-%m-%d') as creation_date"),
                DB::raw('Count(distinct crm_actual_visits.id) as no_visits'),
                DB::raw('IFNULL(group_concat(DATE_FORMAT(crm_actual_visits.visit_date,"%d-%m-%Y"),"  "),"") as visit_date'),
            ]
        )
            ->leftJoin('account_types', 'accounts.type_id', 'account_types.id');
        if (!$setting) {
            $doctors = $doctors
                ->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
                ->leftJoin(
                    'new_account_doctors',
                    function ($join) {
                        $join->on('accounts.id', '=', 'new_account_doctors.account_id');
                        $join->on('account_lines.id', '=', 'new_account_doctors.account_lines_id');
                    }
                );
        } else {
            $doctors = $doctors->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
                ->leftJoin('new_account_doctors', function ($join) use ($lines) {
                    $join->on('accounts.id', 'new_account_doctors.account_id')
                        ->whereIntegerInRaw('new_account_doctors.line_id', $lines)
                        ->where(fn($q) => $q->where('new_account_doctors.to_date', '=', null)
                            ->orWhere('new_account_doctors.to_date', '>=', (string)Carbon::now()));
                });
        }
        $doctors = $doctors->leftJoin('doctors', 'new_account_doctors.doctor_id', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')

            // ->leftJoin('actual_visits', 'doctors.id', 'actual_visits.account_dr_id')
            ->leftJoin('actual_visits', function ($join) use ($from, $to) {
                $join->on('doctors.id', 'actual_visits.account_dr_id')
                    ->whereBetween('visit_date', [$from, $to]);
            })
            ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
            ->leftJoin('classes as d', 'doctors.class_id', 'd.id')
            ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
            ->leftJoin('line_users_divisions', function ($join) {
                $join->on('line_divisions.id', 'line_users_divisions.line_division_id')
                    ->where('line_users_divisions.from_date', '<=', Carbon::now())
                    ->where(fn($q) => $q->where('line_users_divisions.to_date', '=', null)
                        ->orWhere('line_users_divisions.to_date', '>=', (string)Carbon::now()));
            })
            ->leftJoin('users', 'line_users_divisions.user_id', 'users.id')
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->where(fn($q) => $q->where('accounts.inactive_date', '=', null)->orWhere('accounts.inactive_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->where('doctors.inactive_date', '=', null)->orWhere('doctors.inactive_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->where('account_lines.to_date', '=', null)->orWhere('account_lines.to_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->where('new_account_doctors.to_date', '=', null)->orWhere('new_account_doctors.to_date', '>=', (string)Carbon::now()))
            ->where('accounts.active_date', '<=', Carbon::now())
            ->where('doctors.active_date', '<=', Carbon::now())
            ->where('new_account_doctors.from_date', '<=', Carbon::now())
            ->whereNull('account_lines.deleted_at')
            ->whereNull('new_account_doctors.deleted_at')
            ->whereNull('doctors.deleted_at')
            ->groupBy("doctors.id", "specialities.id", 'lines.id', 'line_divisions.id');
        return $this->extracted($lines, $doctors, $divisions, $bricks, $accountTypes, $specialities, $doctor_ids, $shift_id ? [$shift_id] : []);
    }

    public function getDoctorTracing(?array $lines = [], ?array $divisions = [], ?Carbon $from = null, ?Carbon $to = null, ?array $specialities = [], ?array $accountTypes = [], ?array $doctor_ids = [], ?array $bricks = [], ?int $shift_id = null, ?array $userLines = []): Collection
    {
        $user = Auth::user();
        $setting = ListType::first()->type == 'Default List';
        $doctors = Account::select(
            [
                'doctors.id as doctor_id',
                'doctors.id as id',
                DB::raw('IFNULL(group_concat(distinct crm_accounts.name),"") as account'),
                DB::raw('IFNULL(group_concat(distinct crm_accounts.id),"") as account_id'),
                DB::raw('IFNULL(group_concat(distinct crm_accounts.code),"") as acc_code'),
                DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
                // DB::raw('IFNULL(group_concat(distinct crm_line_divisions.name),"") as division'),
                DB::raw('IFNULL(group_concat(distinct crm_users.fullname),"") as employee'),
                DB::raw('IFNULL(group_concat(distinct crm_users.emp_code),"") as emp_code'),
                // DB::raw('IFNULL(group_concat(distinct crm_lines.name),"") as line'),
                DB::raw('IFNULL(group_concat(distinct crm_account_types.name),"") as account_type'),
                DB::raw('IFNULL(group_concat(distinct crm_account_types.shift_id),"") as acc_shift_id'),
                DB::raw('IFNULL(group_concat(distinct crm_account_types.id),"") as account_type_id'),
                DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
                // DB::raw('IFNULL(group_concat(distinct crm_lines.name),"") as line'),
                // DB::raw('IFNULL(group_concat(distinct crm_line_divisions.name),"") as division'),
                'lines.name as line',
                'lines.id as line_id',
                'line_divisions.name as division',
                'line_divisions.id as div_id',
                'doctors.name as doctor',
                'doctors.ucode as ucode',
                DB::raw('IFNULL(group_concat(distinct crm_d.name),"") as class'),
                DB::raw('IFNULL(group_concat(distinct crm_d.id),"") as doc_class_id'),
                'specialities.name as speciality',
                'specialities.id as speciality_id',
                DB::raw("DATE_FORMAT(crm_doctors.active_date,'%Y-%m-%d') as creation_date"),
                DB::raw('Count(distinct crm_actual_visits.id) as no_visits'),
                DB::raw('Count(distinct crm_commercial_doctors.request_id) as no_requests'),
                // DB::raw('group_concat(distinct crm_actual_visits.user_id) as visit_user_id'),
                DB::raw('IFNULL(group_concat(distinct DATE_FORMAT(crm_actual_visits.visit_date,"%d-%m-%Y"),"  "),"") as visit_date'),
                DB::raw('IFNULL(group_concat(distinct crm_actual_users.fullname,"  "),"") as emp_visits'),
            ]
        )
            ->leftJoin('account_types', 'accounts.type_id', 'account_types.id');
        if (!$setting) {
            $doctors = $doctors
                ->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
                ->leftJoin(
                    'new_account_doctors',
                    function ($join) {
                        $join->on('accounts.id', '=', 'new_account_doctors.account_id');
                        $join->on('account_lines.id', '=', 'new_account_doctors.account_lines_id');
                    }
                );
        } else {
            $doctors = $doctors->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
                ->leftJoin('new_account_doctors', function ($join) use ($lines, $to) {
                    $join->on('accounts.id', 'new_account_doctors.account_id')
                        ->whereIntegerInRaw('new_account_doctors.line_id', $lines)
                        ->where(fn($q) => $q->where('new_account_doctors.to_date', '=', null)
                            ->orWhere('new_account_doctors.to_date', '>=', $to ?? (string)Carbon::now()));
                });
        }
        $doctors = $doctors->leftJoin('doctors', 'new_account_doctors.doctor_id', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')

            // ->leftJoin('actual_visits', 'doctors.id', 'actual_visits.account_dr_id')
            // ->leftJoin('actual_visits', function ($join) use ($from, $to) {
            //     $join->on('doctors.id', 'actual_visits.account_dr_id');
            //     // ->whereBetween('visit_date', [$from, $to]);
            // })
            ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
            ->leftJoin('commercial_doctors', 'doctors.id', 'commercial_doctors.doctor_id')
            ->leftJoin('classes as d', 'doctors.class_id', 'd.id')
            ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
            ->leftJoin('line_users_divisions', function ($join) use ($from, $to) {
                $join->on('line_divisions.id', 'line_users_divisions.line_division_id')
                    ->where('line_users_divisions.from_date', '<=', $from?->toDateString() ?? Carbon::now())
                    ->where(fn($q) => $q->where('line_users_divisions.to_date', '=', null)
                        ->orWhere('line_users_divisions.to_date', '>=', $to?->toDateString() ?? (string)Carbon::now()));
            })
            ->leftJoin('users', 'line_users_divisions.user_id', 'users.id')
            ->leftJoin('actual_visits', function ($join) use ($from, $to, $userLines) {
                $join->on('doctors.id', 'actual_visits.account_dr_id')
                    // ->on('users.id', 'actual_visits.user_id')
                    ->wherebetween('visit_date', [$from, $to])
                    ->whereIntegerInRaw('actual_visits.line_id', $userLines);
            })
            ->leftJoin('users as actual_users', 'actual_visits.user_id', 'actual_users.id')
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->where(fn($q) => $q->where('accounts.inactive_date', '=', null)->orWhere('accounts.inactive_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->where('doctors.inactive_date', '=', null)->orWhere('doctors.inactive_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->where('account_lines.to_date', '=', null)->orWhere('account_lines.to_date', '>=', $to?->toDateString() ?? (string)Carbon::now()))
            ->where(fn($q) => $q->where('new_account_doctors.to_date', '=', null)->orWhere('new_account_doctors.to_date', '>=', $to?->toDateString() ?? (string)Carbon::now()))
            ->where('accounts.active_date', '<=', Carbon::now())
            ->where('doctors.active_date', '<=', Carbon::now())
            ->where('new_account_doctors.from_date', '<=', $to?->toDateString() ?? Carbon::now())
            ->whereNull('account_lines.deleted_at')
            ->whereNull('new_account_doctors.deleted_at')
            ->whereNull('doctors.deleted_at')
            ->groupBy("doctors.id", "specialities.id", 'lines.id', 'line_divisions.id');
        return $this->extracted($lines, $doctors, $divisions, $bricks, $accountTypes, $specialities, $doctor_ids,  $shift_id ? [$shift_id] : []);
    }

    public function getDoctorsWithFrequency(
        ?array  $lines = [],
        ?array  $divisions = [],
        ?Carbon $from = null,
        ?Carbon $to = null,
        array   $months = [],
        ?string $year = null,
        ?array  $specialities = [],
        ?array  $accountTypes = [],
        ?array  $doctor_ids = [],
        ?array  $bricks = [],
        ?int    $shift = null
    ): Collection {
        $setting = ListType::first()->type == 'Default List';

        $FrequencyType = OtherSetting::where('key', 'type_of_frequency')->value('value');
        $doctors = Account::select(
            [
                'doctors.id as doctor_id',
                'doctors.id as id',
                DB::raw('IFNULL(group_concat(distinct crm_accounts.name),"") as account'),
                DB::raw('IFNULL(group_concat(distinct crm_accounts.id),"") as account_id'),
                DB::raw('IFNULL(group_concat(distinct crm_accounts.code),"") as acc_code'),
                DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
                DB::raw('IFNULL(group_concat(distinct crm_users.fullname),"") as employee'),
                DB::raw('IFNULL(group_concat(distinct crm_account_types.name),"") as account_type'),
                DB::raw('IFNULL(group_concat(distinct crm_account_types.shift_id),"") as acc_shift_id'),
                DB::raw('IFNULL(group_concat(distinct crm_account_types.id),"") as account_type_id'),
                DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
                'lines.name as line',
                'lines.id as line_id',
                'line_divisions.name as division',
                'line_divisions.id as div_id',
                'doctors.name as doctor',
                'doctors.ucode as ucode',
                'd.name as class',
                'd.id as doc_class_id',
                'specialities.name as speciality',
                'specialities.id as speciality_id',
                DB::raw("DATE_FORMAT(crm_doctors.active_date,'%Y-%m-%d') as creation_date"),
                DB::raw('Count(distinct crm_actual_visits.id) as no_visits'),
                DB::raw('IFNULL(group_concat(DATE_FORMAT(crm_actual_visits.visit_date,"%d-%m-%Y"),"  "),"") as visit_date'),
            ]
        )
            ->join('account_types', function ($join) use ($accountTypes, $shift) {
                $join->on('accounts.type_id', 'account_types.id');
                if (!empty($accountTypes)) {
                    $join->whereIntegerInRaw('account_types.id', $accountTypes);
                }
                if ($shift) {
                    $join->where("account_types.shift_id", $shift);
                }
            })
            ->join('account_lines', function ($join) use ($lines, $divisions, $bricks, $from, $to) {
                $join->on('accounts.id', 'account_lines.account_id')
                    ->whereIntegerInRaw('account_lines.line_id', $lines)
                    ->whereNull('account_lines.deleted_at')
                    ->where(function ($query) use ($from, $to) {
                        $query->where(function ($subQuery) use ($from, $to) {
                            $subQuery->whereNull('account_lines.to_date') // Active records
                                ->orWhereBetween('account_lines.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                                ->orWhere('account_lines.to_date', '>=', $to->toDateString()); // Ends within range
                        })
                            ->where(function ($subQuery) use ($from, $to) {
                                $subQuery->where('account_lines.from_date', '<=', $from->toDateString()) // Starts before range
                                    ->orWhereBetween('account_lines.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                            });
                    })
                    ->whereIntegerInRaw('account_lines.line_division_id', $divisions);
                if (!empty($bricks)) {
                    $join->whereIntegerInRaw('account_lines.brick_id', $bricks);
                }
            });
        if (!$setting) {
            $doctors = $doctors
                ->join(
                    'new_account_doctors',
                    function ($join) use ($lines, $to, $from) {
                        $join->on('accounts.id', '=', 'new_account_doctors.account_id');
                        $join->on('account_lines.id', '=', 'new_account_doctors.account_lines_id')
                            ->whereIntegerInRaw('new_account_doctors.line_id', $lines)
                            ->whereNull('new_account_doctors.deleted_at')
                            ->where(function ($query) use ($from, $to) {
                                $query->where(function ($subQuery) use ($from, $to) {
                                    $subQuery->whereNull('new_account_doctors.to_date') // Active records
                                        ->orWhereBetween('new_account_doctors.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                                        ->orWhere('new_account_doctors.to_date', '>=', $to->toDateString()); // Ends within range
                                })
                                    ->where(function ($subQuery) use ($from, $to) {
                                        $subQuery->where('new_account_doctors.from_date', '<=', $from->toDateString()) // Starts before range
                                            ->orWhereBetween('new_account_doctors.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                                    });
                            });
                    }
                );
        } else {
            $doctors = $doctors
                ->join('new_account_doctors', function ($join) use ($lines, $from, $to) {
                    $join->on('accounts.id', 'new_account_doctors.account_id')
                        ->whereIntegerInRaw('new_account_doctors.line_id', $lines)
                        ->whereNull('new_account_doctors.deleted_at')
                        ->where(function ($query) use ($from, $to) {
                            $query->where(function ($subQuery) use ($from, $to) {
                                $subQuery->whereNull('new_account_doctors.to_date') // Active records
                                    ->orWhereBetween('new_account_doctors.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                                    ->orWhere('new_account_doctors.to_date', '>=', $to->toDateString()); // Ends within range
                            })
                                ->where(function ($subQuery) use ($from, $to) {
                                    $subQuery->where('new_account_doctors.from_date', '<=', $from->toDateString()) // Starts before range
                                        ->orWhereBetween('new_account_doctors.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                                });
                        });
                });
        }
        $doctors = $doctors->join('doctors', function ($join) use ($specialities) {
            $join->on('new_account_doctors.doctor_id', 'doctors.id')
                ->whereNull('doctors.deleted_at');
            if (!empty($specialities)) {
                $join->whereIntegerInRaw('doctors.speciality_id', $specialities);
            }
        })
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')

            ->leftJoin('actual_visits', function ($join) use ($from, $to) {
                $join->on('doctors.id', 'actual_visits.account_dr_id')
                    ->whereBetween('visit_date', [$from, $to]);
            })
            ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
            ->leftJoin('classes as d', 'doctors.class_id', 'd.id')
            ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
            ->leftJoin('line_users_divisions', function ($join) {
                $join->on('line_divisions.id', 'line_users_divisions.line_division_id')
                    ->where('line_users_divisions.from_date', '<=', Carbon::now())
                    ->where(fn($q) => $q->where('line_users_divisions.to_date', '=', null)
                        ->orWhere('line_users_divisions.to_date', '>=', (string)Carbon::now()));
            })
            ->leftJoin('users', 'line_users_divisions.user_id', 'users.id')
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->where(fn($q) => $q->where('accounts.inactive_date', '=', null)->orWhere('accounts.inactive_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->where('doctors.inactive_date', '=', null)->orWhere('doctors.inactive_date', '>=', (string)Carbon::now()))

            ->where('accounts.active_date', '<=', Carbon::now())
            ->where('doctors.active_date', '<=', Carbon::now())
            ->whereNull('doctors.deleted_at');
        if ($FrequencyType == 1) {
            $doctors = $doctors
                ->selectRaw('CAST(
                    IFNULL(
                        sum(crm_class_frequencies.frequency)/Count(distinct crm_actual_visits.id),
                        sum(crm_class_frequencies.frequency)
                        ) AS DECIMAL(10,0)
                        ) as frequency')
                ->leftJoin('class_frequencies', function ($join) use ($lines, $months, $year) {
                    $join
                        ->on('d.id', 'class_frequencies.class_id')
                        ->whereIn('class_frequencies.line_id', $lines)
                        ->whereNull('class_frequencies.deleted_at')
                        ->whereBetween(DB::raw("(DATE_FORMAT(crm_class_frequencies.date,'%m'))"), $months)
                        ->whereYear('class_frequencies.date', $year)->groupBy('class_frequencies.id');
                });
        }
        if ($FrequencyType == 2) {
            $doctors = $doctors->selectRaw('CAST(
                            IFNULL(
                                sum(crm_doctor_frequencies.frequency)/Count(distinct crm_actual_visits.id),
                                sum(crm_doctor_frequencies.frequency)
                                ) AS DECIMAL(10,0)
                                ) as frequency')
                ->leftJoin('doctor_frequencies', function ($join) use ($lines, $months, $year,) {
                    $join->on('doctors.id', 'doctor_frequencies.doctor_id')
                        ->whereIntegerInRaw('doctor_frequencies.line_id', $lines)
                        ->whereNull('doctor_frequencies.deleted_at')
                        ->whereBetween(DB::raw("(DATE_FORMAT(crm_doctor_frequencies.date,'%m'))"), $months)
                        ->whereYear('doctor_frequencies.date', $year);
                });
        }
        if ($FrequencyType == 3) {
            $doctors = $doctors->selectRaw('CAST(
                                    IFNULL(
                                        sum(crm_speciality_frequencies.frequency)/Count(distinct crm_actual_visits.id),
                                        sum(crm_speciality_frequencies.frequency)
                                        ) AS DECIMAL(10,0)
                                        ) as frequency')
                ->leftJoin('speciality_frequencies', function ($join) use ($lines, $months, $year,) {
                    $join->on('doctors.speciality_id', 'speciality_frequencies.speciality_id')
                        ->whereIntegerInRaw('speciality_frequencies.line_id', $lines)
                        ->whereNull('speciality_frequencies.deleted_at')
                        ->whereBetween(DB::raw("(DATE_FORMAT(crm_speciality_frequencies.date,'%m'))"), $months)
                        ->whereYear('speciality_frequencies.date', $year);
                });
        }
        if ($FrequencyType == 4) {
            $doctors = $doctors->selectRaw('CAST(
                IFNULL(
                sum(crm_speciality_class_frequencies.frequency)/Count(distinct crm_actual_visits.id),
                sum(crm_speciality_class_frequencies.frequency)
                ) AS DECIMAL(10,0)
                ) as frequency')
                ->leftJoin('speciality_class_frequencies', function ($join) use ($lines, $months, $year,) {
                    $join->on('doctors.speciality_id', 'speciality_class_frequencies.speciality_id')
                        ->on('doctors.class_id', 'speciality_class_frequencies.class_id')
                        ->whereIntegerInRaw('speciality_class_frequencies.line_id', $lines)
                        ->whereBetween(DB::raw("(DATE_FORMAT(crm_speciality_class_frequencies.date,'%m'))"), $months)
                        ->whereYear('speciality_class_frequencies.date', $year);
                });
        }
        $doctors = $doctors->groupBy("doctors.id", "specialities.id", 'lines.id', 'line_divisions.id');
        return $this->extracted($lines, $doctors, $divisions, $bricks, $accountTypes, $specialities, $doctor_ids, $shift ? [$shift] : []);
    }

    /**
     * @param array|null $lines
     * @param  $doctors
     * @param array|null $divisions
     * @param array|null $bricks
     * @param array|null $accountTypes
     * @param array|null $specialities
     * @param array|null $doctor_ids
     * @return Collection
     */
    private function extracted(?array $lines, $doctors, ?array $divisions, ?array $bricks, ?array $accountTypes, ?array $specialities, ?array $doctor_ids, ?array $shifts): Collection
    {
        if (!empty($lines)) {
            $doctors->whereIntegerInRaw('new_account_doctors.line_id', $lines)->whereIntegerInRaw('account_lines.line_id', $lines);
        }
        if (!empty($divisions)) {
            $doctors->whereIntegerInRaw('line_divisions.id', $divisions);
        }
        if (!empty($bricks)) {
            $doctors->whereIntegerInRaw('account_lines.brick_id', $bricks);
        }
        if (!empty($accountTypes)) {
            $doctors->whereIntegerInRaw('accounts.type_id', $accountTypes);
        }
        if (!empty($specialities)) {
            $doctors->whereIntegerInRaw('specialities.id', $specialities);
        }
        if (!empty($doctor_ids)) {
            $doctors->whereIntegerInRaw('doctors.id', $doctor_ids);
        }
        if (!empty($shifts)) {
            $doctors->whereIntegerInRaw("account_types.shift_id", $shifts);
        }
        return $doctors->groupBy('doctors.id')->get();
    }


    public function getDoctorsِCoverage(?array $lines = [], ?array $divisions = [], ?Carbon $from = null, ?Carbon $to = null, ?array $specialities = [], ?array $accountTypes = [], ?array $doctor_ids = [], ?array $bricks = [], ?array $shifts = [], ?array $classes = []): Collection
    {
        $setting = ListType::first()->type == 'Default List';
        $doctors = Account::select(
            [
                'doctors.id as doctor_id',
                'doctors.id as id',
                DB::raw('IFNULL(group_concat(distinct crm_accounts.name),"") as account'),
                DB::raw('IFNULL(group_concat(distinct crm_accounts.id),"") as account_id'),
                DB::raw('IFNULL(group_concat(distinct crm_accounts.code),"") as acc_code'),
                DB::raw('IFNULL(group_concat(distinct crm_users.fullname),"") as employee'),
                DB::raw('IFNULL(group_concat(distinct crm_account_types.name),"") as account_type'),
                DB::raw('IFNULL(group_concat(distinct crm_account_types.shift_id),"") as acc_shift_id'),
                DB::raw('IFNULL(group_concat(distinct crm_account_types.id),"") as account_type_id'),
                DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
                DB::raw('IFNULL(group_concat(distinct crm_lines.name),"") as line'),
                DB::raw('IFNULL(group_concat(distinct crm_lines.id),"") as line_id'),
                DB::raw('IFNULL(group_concat(distinct crm_line_divisions.name),"") as division'),
                DB::raw('IFNULL(group_concat(distinct crm_line_divisions.id),"") as div_id'),
                // 'lines.name as line',
                // 'lines.id as line_id',
                // 'line_divisions.name as division',
                // 'line_divisions.id as div_id',
                'doctors.name as doctor',
                'doctors.ucode as ucode',
                DB::raw('IFNULL(group_concat(distinct crm_d.name),"") as class'),
                DB::raw('IFNULL(group_concat(distinct crm_d.id),"") as doc_class_id'),
                'specialities.name as speciality',
                'specialities.id as speciality_id',
                DB::raw("DATE_FORMAT(crm_doctors.active_date,'%Y-%m-%d') as creation_date"),
                DB::raw('Count(distinct crm_actual_visits.id) as no_visits'),
                DB::raw('IFNULL(group_concat(DATE_FORMAT(crm_actual_visits.visit_date,"%d-%m-%Y"),"  "),"") as visit_date'),
            ]
        )
            ->where('accounts.active_date', '<=', Carbon::now())
            ->where(fn($q) => $q->where('accounts.inactive_date', '=', null)->orWhere('accounts.inactive_date', '>=', (string)Carbon::now()))
            ->join('account_types', function ($join) use ($accountTypes, $shifts) {
                $join->on('accounts.type_id', 'account_types.id');
                if (!empty($accountTypes)) {
                    $join->whereIntegerInRaw('account_types.id', $accountTypes);
                }
                if (!empty($shifts)) {
                    $join->whereIntegerInRaw("account_types.shift_id", $shifts);
                }
            })
            ->join('account_lines', function ($join) use ($lines, $divisions, $bricks, $from, $to) {
                $join->on('accounts.id', 'account_lines.account_id')
                    ->whereIntegerInRaw('account_lines.line_id', $lines)
                    ->whereNull('account_lines.deleted_at')
                    // ->where('account_lines.from_date', '<=', Carbon::now())
                    // ->where(fn($q) => $q->whereNull('account_lines.to_date')
                    //     ->orWhere('account_lines.to_date', '>=', $to->toDateString()))
                    ->where(function ($query) use ($from, $to) {
                        $query->where(function ($subQuery) use ($from, $to) {
                            $subQuery->whereNull('account_lines.to_date') // Active records
                                ->orWhereBetween('account_lines.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                                ->orWhere('account_lines.to_date', '>=', $to->toDateString()); // Ends within range
                        })
                            ->where(function ($subQuery) use ($from, $to) {
                                $subQuery->where('account_lines.from_date', '<=', $from->toDateString()) // Starts before range
                                    ->orWhereBetween('account_lines.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                            });
                    })
                    ->whereIntegerInRaw('account_lines.line_division_id', $divisions);
                if (!empty($bricks)) {
                    $join->whereIntegerInRaw('account_lines.brick_id', $bricks);
                }
            });
        if (!$setting) {
            $doctors = $doctors
                ->join(
                    'new_account_doctors',
                    function ($join) use ($lines, $to, $from) {
                        $join->on('accounts.id', '=', 'new_account_doctors.account_id');
                        $join->on('account_lines.id', '=', 'new_account_doctors.account_lines_id')
                            ->whereIntegerInRaw('new_account_doctors.line_id', $lines)
                            ->whereNull('new_account_doctors.deleted_at')
                            // ->where('new_account_doctors.from_date', '<=', $from->toDateString())
                            // ->where(fn($q) => $q->whereNull('new_account_doctors.to_date')
                            //     ->orWhere('new_account_doctors.to_date', '>=', $to->toDateString()));
                            ->where(function ($query) use ($from, $to) {
                                $query->where(function ($subQuery) use ($from, $to) {
                                    $subQuery->whereNull('new_account_doctors.to_date') // Active records
                                        ->orWhereBetween('new_account_doctors.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                                        ->orWhere('new_account_doctors.to_date', '>=', $to->toDateString()); // Ends within range
                                })
                                    ->where(function ($subQuery) use ($from, $to) {
                                        $subQuery->where('new_account_doctors.from_date', '<=', $from->toDateString()) // Starts before range
                                            ->orWhereBetween('new_account_doctors.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                                    });
                            });
                    }
                );
        } else {
            $doctors = $doctors
                ->join('new_account_doctors', function ($join) use ($lines, $from, $to) {
                    $join->on('accounts.id', 'new_account_doctors.account_id')
                        ->whereIntegerInRaw('new_account_doctors.line_id', $lines)
                        ->whereNull('new_account_doctors.deleted_at')
                        ->where(function ($query) use ($from, $to) {
                            $query->where(function ($subQuery) use ($from, $to) {
                                $subQuery->whereNull('new_account_doctors.to_date') // Active records
                                    ->orWhereBetween('new_account_doctors.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                                    ->orWhere('new_account_doctors.to_date', '>=', $to->toDateString()); // Ends within range
                            })
                                ->where(function ($subQuery) use ($from, $to) {
                                    $subQuery->where('new_account_doctors.from_date', '<=', $from->toDateString()) // Starts before range
                                        ->orWhereBetween('new_account_doctors.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                                });
                        });
                });
        }
        $doctors = $doctors->join('doctors', function ($join) use ($specialities) {
            $join->on('new_account_doctors.doctor_id', 'doctors.id')
                ->whereNull('doctors.deleted_at');
            if (!empty($specialities)) {
                $join->whereIntegerInRaw('doctors.speciality_id', $specialities);
            }
        })
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')

            ->leftJoin('actual_visits', function ($join) use ($from, $to) {
                $join->on('doctors.id', 'actual_visits.account_dr_id')
                    ->whereBetween('visit_date', [$from, $to]);
            })
            ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
            ->leftJoin('classes as d', 'doctors.class_id', 'd.id')
            ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
            ->leftJoin('line_users_divisions', function ($join) {
                $join->on('line_divisions.id', 'line_users_divisions.line_division_id')
                    ->where('line_users_divisions.from_date', '<=', Carbon::now())
                    ->where(fn($q) => $q->where('line_users_divisions.to_date', '=', null)
                        ->orWhere('line_users_divisions.to_date', '>=', (string)Carbon::now()));
            })
            ->leftJoin('users', 'line_users_divisions.user_id', 'users.id')
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->where('doctors.active_date', '<=', Carbon::now())
            ->where(fn($q) => $q->where('doctors.inactive_date', '=', null)->orWhere('doctors.inactive_date', '>=', (string)Carbon::now()));

        if (!empty($classes)) {
            $doctors->whereIntegerInRaw('doctors.class_id', $classes);
        }
        $doctors = $doctors->groupBy("doctors.id", "specialities.id");
        return $doctors->get();
    }
}
