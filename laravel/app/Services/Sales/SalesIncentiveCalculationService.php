<?php

namespace App\Services\Sales;

use App\DivisionType;
use App\Line;
use App\LineDivision;
use App\LineDivisionUser;
use App\LineProduct;
use App\Models\Distributors\DistributorLine;
use App\SalesTypes;
use App\Services\Reports\SalesIncentives\Fields\SalesIncentiveReportFields;
use App\Services\Reports\SalesIncentives\IncentiveMonthlyViewReportService;
use App\Services\Reports\SalesIncentives\IncentiveReportTypes;
use App\Services\Reports\SalesIncentives\IncentiveSalesViewReportService;
use App\User;
use Carbon\CarbonPeriod;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use App\Services\Sales\Strategies\Legacy\DistrictLegacyIncentiveStrategy;
use App\Services\Sales\Strategies\Legacy\AreaLegacyIncentiveStrategy;
use App\Services\Sales\Strategies\Legacy\CountryLegacyIncentiveStrategy;
use App\Services\Sales\Strategies\Legacy\LegacyIncentiveStrategy;
use App\Services\Sales\Strategies\ProductFiltering\ProductFilteringStrategy;
use App\Services\Sales\Strategies\ProductFiltering\VisibleProductFilteringStrategy;
use App\Services\Sales\Strategies\ProductFiltering\VisibleBrandProductFilteringStrategy;
use App\Services\Sales\Strategies\ProductFiltering\HiddenProductFilteringStrategy;
use App\Services\Sales\Strategies\ProductFiltering\DefaultProductFilteringStrategy;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;

/**
 * Class SalesIncentiveCalculationService
 *
 * Orchestrates the calculation of sales incentives based on various filters and data sources.
 * It leverages strategy patterns for legacy incentive calculations and product filtering,
 * and uses other services for specific view reports and data holding.
 */
class SalesIncentiveCalculationService
{

    private int $cacheTimeout = 60;
    private int $perDivOrUserFilter;
    private IncentiveReportTypes $view;
    private Carbon $from;
    private Carbon $to;
    private bool $isChecked;
    private string $fromYear;
    private array $years = [];
    private array $months = [];
    private CarbonPeriod $period;
    private array $lineIds;
    private User $authUser;
    private array $divisionIds;
    private array $userIds;
    private array $filters;
    private mixed $type; // Type for product filtering (e.g., 1 for visible, 2 for brand, etc.)
    private array $productIds;

    private LegacyIncentiveStrategy $districtLegacyStrategy;
    private LegacyIncentiveStrategy $areaLegacyStrategy;
    private LegacyIncentiveStrategy $countryLegacyStrategy;
    private LinkedPositionIncentiveService $linkedPositionIncentiveService;

    /**
     * SalesIncentiveCalculationService constructor.
     *
     * @param IncentiveSalesViewReportService $salesViewReportService Service for sales view reports.
     * @param IncentiveMonthlyViewReportService $salesMonthlyViewReportService Service for monthly view reports.
     * @param SalesIncentiveReportFields $salesIncentivesReportFields Service for defining report fields.
     * @param SalesIncentiveHolder $salesIncentivesHolder Service for holding and caching incentive data.
     * @param LinkedPositionIncentiveService $linkedPositionIncentiveService Service for handling linked position incentives.
     */
    public function __construct(
        private readonly IncentiveSalesViewReportService   $salesViewReportService,
        private readonly IncentiveMonthlyViewReportService $salesMonthlyViewReportService,
        private readonly SalesIncentiveReportFields        $salesIncentivesReportFields,
        private readonly SalesIncentiveHolder              $salesIncentivesHolder,
        LinkedPositionIncentiveService                     $linkedPositionIncentiveService
    )
    {
        $this->districtLegacyStrategy = new DistrictLegacyIncentiveStrategy();
        $this->areaLegacyStrategy = new AreaLegacyIncentiveStrategy();
        $this->countryLegacyStrategy = new CountryLegacyIncentiveStrategy();
        $this->linkedPositionIncentiveService = $linkedPositionIncentiveService;
    }

    /**
     * Generates a cache key based on a prefix and parameters.
     *
     * @param string $prefix The prefix for the cache key.
     * @param array $params An array of parameters to include in the key.
     * @return string The generated cache key.
     */
    private function generateCacheKey(string $prefix, array $params): string
    {
        return $prefix . ':' . implode('_', $params);
    }

    /**
     * Retrieves lines and mapping types relevant to the authenticated user and date range.
     * Results are cached.
     *
     * @param Carbon|string $from The start date of the period.
     * @param Carbon|string $to The end date of the period.
     * @return array An array containing 'lines' and 'mappingTypes'.
     */
    public function getLines($from, $to): array
    {
        $user = Auth::user();
        $cacheKey = $this->generateCacheKey('user_lines', [$user->id]);
        $lines = Cache::remember($cacheKey, $this->cacheTimeout, fn() => $user->userLines($from, $to));
        $mappingTypes = Cache::remember('mapping_types', $this->cacheTimeout, fn() => SalesTypes::get());
        return compact('lines', 'mappingTypes');
    }

    /**
     * Retrieves detailed data for specified lines, including users, divisions, products, and distributors.
     * Results are cached.
     *
     * @param array $lineIds Array of line IDs.
     * @param Carbon|null $from Optional start date for filtering.
     * @param Carbon|null $to Optional end date for filtering.
     * @return array An array containing 'users', 'divisions', 'products', 'distributors'.
     */
    public function getLineData(array $lineIds, ?Carbon $from = null, ?Carbon $to = null): array
    {
        $user = Auth::user();
        $cacheKey = $this->generateCacheKey('line_data', array_merge($lineIds, [$user->id, $from ? $from->timestamp : '', $to ? $to->timestamp : '']));
        return Cache::remember($cacheKey, $this->cacheTimeout, function () use ($user, $lineIds, $from, $to) {
            $lines = Line::whereIn('id', $lineIds)->get();
            $lineDivisionIdsQuery = LineDivisionUser::where('user_id', $user->id)
                ->whereIn('line_id', $lineIds);
            if ($from && $to) {
                $lineDivisionIdsQuery->where('from_date', '<=', $to)
                    ->where(fn($q) => $q->whereNull('to_date')->orWhere('to_date', '>=', $from));
            }
            $lineDivisionIds = $lineDivisionIdsQuery->pluck('line_division_id');
            $divisions = LineDivision::whereIn('id', $lineDivisionIds->unique()->toArray())->get();
            $users = $user->belowUsersOfAllLinesWithPositions($lines, 'Active', $from, $to);
            $distributors = $this->getDistributors($lineIds, $from, $to);
            $products = $this->getProducts($lineIds, $from, $to);
            return compact('users', 'divisions', 'products', 'distributors');
        });
    }

    /**
     * Retrieves distributors associated with given line IDs and date range.
     * Results are cached.
     *
     * @param array $lineIds Array of line IDs.
     * @param Carbon|string $from Start date.
     * @param Carbon|string $to End date.
     * @return Collection Collection of distributors.
     */
    private function getDistributors($lineIds, $from, $to): Collection
    {
        $cacheKey = $this->generateCacheKey('load_distributors_in_lines', $lineIds);
        return Cache::remember($cacheKey, $this->cacheTimeout, function () use ($lineIds, $from, $to) {
            return DistributorLine::whereIntegerInRaw('line_id', $lineIds)
                ->where('from_date', '<=', $from ?? (string)Carbon::now())
                ->where(fn($q) => $q->whereNull('to_date')->orWhere('to_date', '>=', $to->toDateString() ?? (string)Carbon::now()))
                ->with('distributor')->get()->pluck('distributor');
        });
    }

    /**
     * Retrieves products associated with given line IDs and date range.
     *
     * @param array $lineIds Array of line IDs.
     * @param Carbon|string $from Start date.
     * @param Carbon|string $to End date.
     * @return Collection Collection of products.
     */
    private function getProducts($lineIds, $from, $to): Collection
    {
        return LineProduct::whereIntegerInRaw('line_id', $lineIds)
            ->where('from_date', '<=', $from ?? (string)Carbon::now())
            ->where(fn($q) => $q->whereNull('to_date')->orWhere('to_date', '>=', $to->toDateString() ?? (string)Carbon::now()))
            ->with('product')->get()->pluck('product')->unique('id')->values();
    }

    /**
     * Applies a date range filter to an Eloquent query builder instance.
     *
     * @param EloquentBuilder $query The query builder instance.
     * @param Carbon $from The start date of the range.
     * @param Carbon $to The end date of the range.
     * @param string $prefix Optional prefix for date columns if they are ambiguous (e.g., 'table_name').
     * @return Builder The modified query builder.
     */
    private function applyDateRangeFilter(EloquentBuilder $query, Carbon $from, Carbon $to, $prefix = ''): EloquentBuilder
    {
        $fromDateColumn = $prefix ? "$prefix.from_date" : 'from_date';
        $toDateColumn = $prefix ? "$prefix.to_date" : 'to_date';

        return $query->where(function ($query) use ($from, $to, $fromDateColumn, $toDateColumn) {
            $query->where(function ($subQuery) use ($from, $to, $toDateColumn) {
                $subQuery->whereNull($toDateColumn)
                    ->orWhereBetween($toDateColumn, [$from->toDateString(), $to->toDateString()])
                    ->orWhere($toDateColumn, '>=', $to->toDateString());
            })
                ->where(function ($subQuery) use ($from, $to, $fromDateColumn) {
                    $subQuery->where($fromDateColumn, '<=', $from->toDateString())
                        ->orWhereBetween($fromDateColumn, [$from->toDateString(), $to->toDateString()]);
                });
        });
    }

    /**
     * Retrieves product data for specified lines, filtered by type ID, and date range.
     * Results are cached.
     *
     * @param array $lines Array of line IDs.
     * @param int $typeId The type ID for product filtering.
     * @param Carbon|string $from Start date.
     * @param Carbon|string $to End date.
     * @return Collection Collection of filtered products.
     */
    public function getProductData(array $lines, int $typeId, $from, $to): Collection
    {
        $cacheKey = $this->generateCacheKey('product_data', array_merge($lines, [$typeId]));
        return Cache::remember($cacheKey, $this->cacheTimeout, function () use ($lines, $typeId, $from, $to) {
            $query = LineProduct::query()->whereIntegerInRaw('line_id', $lines); // Use LineProduct::query() to start Eloquent query
            $query = $this->applyDateRangeFilter($query, Carbon::parse($from), Carbon::parse($to), 'line_products');
            return $this->filterProductsByType($query, $typeId); // Ensure typeId is int
        });
    }

    /**
     * Returns the appropriate product filtering strategy based on the type ID.
     *
     * @param int $typeId The type ID for product filtering.
     * @return ProductFilteringStrategy The concrete product filtering strategy.
     */
    private function getProductFilteringStrategy(int $typeId): ProductFilteringStrategy
    {
        return match ($typeId) {
            1 => new VisibleProductFilteringStrategy($this->productIds, $this->fromYear, $this->months),
            2 => new VisibleBrandProductFilteringStrategy($this->productIds, $this->fromYear, $this->months),
            3 => new HiddenProductFilteringStrategy($this->productIds, $this->fromYear, $this->months),
            default => new DefaultProductFilteringStrategy($this->productIds, $this->fromYear, $this->months),
        };
    }

    /**
     * Filters products based on the provided query and type ID using a strategy pattern.
     *
     * @param EloquentBuilder $query The base Eloquent query builder instance (for LineProduct).
     * @param int $typeId The type ID that determines the filtering strategy.
     * @return Collection The filtered collection of products.
     */
    private function filterProductsByType(EloquentBuilder $query, int $typeId): Collection
    {
        $strategy = $this->getProductFilteringStrategy($typeId);
        return $strategy->filter($query);
    }

    /**
     * Initializes filters and common service configurations for incentive calculations.
     *
     * @param array $filters An array of filter parameters.
     * @return void
     */
    public function initializeFilter(array $filters): void
    {
        $this->from = Carbon::parse($filters['fromDate'])->startOfDay();
        $this->to = Carbon::parse($filters['toDate'])->endOfDay();
        $this->fromYear = (clone $this->from)->format('Y');
        $this->lineIds = (array)($filters['lines'] ?? []); // Ensure it's an array
        $this->perDivOrUserFilter = (int)($filters['filter'] ?? 1); // Default to 1 if not set
        $this->view = IncentiveReportTypes::tryFrom((string)($filters['view'] ?? '')); // Ensure string, handle potential null
        $this->isChecked = (bool)($filters['checked'] ?? false);
        $this->period = CarbonPeriod::create($this->from, '1 month', $this->to);

        $this->months = [];
        $this->years = [];
        foreach ($this->period as $date) {
            $this->months[] = $date->format("m");
            $this->years[] = $date->format("Y");
        }
        $this->months = array_unique($this->months);
        $this->years = array_unique($this->years);

        $this->type = $filters['type'] ?? null; // Product filtering type
        $this->productIds = (array)($filters['products'] ?? []);
        $this->divisionIds = (array)($filters['divisions'] ?? []);
        $this->userIds = (array)($filters['users'] ?? []);
        $this->filters = $filters; // Store original filters

        $this->initializeReportService((string)($filters['mappingType'] ?? ''));
    }

    /**
     * Initializes report services (SalesIncentiveHolder, view-specific services) with common configurations.
     *
     * @param string $mappingType The mapping type for incentives.
     * @return void
     */
    private function initializeReportService(string $mappingType): void
    {
        $fromMonth = [(clone $this->from)->format('m'), (clone $this->to)->format('m')];
        $commonConfig = [
            'from' => $this->from, 'to' => $this->to, 'months' => $this->months, 'years' => $this->years,
            'isChecked' => $this->isChecked, 'cacheTimeout' => $this->cacheTimeout,
            'perDivOrUserFilter' => $this->perDivOrUserFilter, 'divisionType' => $this->getRootDivisionTypeId(),
            'mappingType' => $mappingType, 'fromYear' => $this->fromYear,
        ];
        $this->salesIncentivesHolder->init(array_merge($commonConfig, ['fromMonth' => $fromMonth, 'period' => $this->period]));

        if ($this->view instanceof IncentiveReportTypes) { // Check if $this->view is valid
            match ($this->view) {
                IncentiveReportTypes::SALES_VIEW => $this->salesViewReportService->configureReportService($commonConfig),
                IncentiveReportTypes::MONTHS_VIEW => $this->salesMonthlyViewReportService->configureReportService(array_merge($commonConfig, ['fromMonth' => $fromMonth, 'period' => $this->period])),
                // Potentially add a default case or throw an exception if view is not matched
            };
        } else {
            // Handle cases where $this->view might not be a valid IncentiveReportTypes enum
            // For example, log an error or throw an exception.
            // This depends on application requirements for invalid view types.
            throw new InvalidArgumentException('Invalid view type for incentive report.');
        }
    }

    /**
     * Retrieves the ID of the root division type (last level).
     * Results are cached.
     *
     * @return int The ID of the root division type.
     */
    public function getRootDivisionTypeId(): int
    {
        return Cache::remember('division_type', $this->cacheTimeout, fn() => DivisionType::where('last_level', 1)->firstOrFail()->id);
    }

    /**
     * Main method to filter and calculate sales incentive data.
     *
     * @param array $saleFilter An array of filter parameters.
     * @return array An array containing the processed incentive 'data' and report 'fields'.
     * @throws \RuntimeException If the user for filtering is invalid or unauthorized.
     */
    public function filter(array $saleFilter): array
    {
        $userIdForFilter = $saleFilter['user_id'] ?? null;
        $user = $userIdForFilter ? User::find($userIdForFilter) : Auth::user();
        if (!$user instanceof User) {
            throw new \RuntimeException('Invalid or unauthorized user for incentive calculation.');
        }
        $this->authUser = $user;

        $this->initializeFilter($saleFilter);
        $fields = $this->salesIncentivesReportFields->getFields($this->view, $this->period, $this->perDivOrUserFilter === 1, $this->isChecked);
        $data = $this->processLines();
        return ['data' => $this->responseData($data)->values(), 'fields' => $fields];
    }

    /**
     * Processes each line to calculate incentive data.
     *
     * @return Collection A collection of processed incentive data items.
     */
    private function processLines(): Collection
    {
        $data = collect();
        $lines = Line::whereIn('id', $this->lineIds)->with([
            'divisions' => fn($query) => $query->where('line_divisions.from_date', '<=', $this->to)
                ->where(fn($q) => $q->whereNull('line_divisions.to_date')
                    ->orWhere('line_divisions.to_date', '>=', $this->from)
                )
                ->where('is_kol', 0),
            'products' => fn($query) => $query->where('line_products.from_date', '<=', $this->to)
                ->where(fn($q) => $q->whereNull('line_products.to_date')
                    ->orWhere('line_products.to_date', '>=', $this->from)
                )->when(!empty($this->productIds), fn($q) => $q->whereIn('line_products.product_id', $this->productIds)),
            'users', // Assuming 'users' relation on Line model is correctly defined
            'distributors' => fn($query) => $query->where(fn($q) => $q->whereNull("line_distributors.to_date")->orWhere("line_distributors.to_date", ">=", now())),
        ])->get();

        $userSpecificDivisions = ($this->perDivOrUserFilter === 2 && !empty($this->userIds)) ? $this->getAllDivisionsForSelectedUsers($this->lineIds, $this->userIds) : collect();

        foreach ($lines as $line) {
            $lineDivisions = $line->divisions;
            $lineProducts = $line->products;
            $filteredDivisions = $this->getFilteredObjects($line, $lineDivisions, $userSpecificDivisions);
            $filteredProducts = $this->getFilteredProducts($line, $lineProducts); // $this->type is used internally by getFilteredProducts
            $data = $data->merge($this->processFilteredObjects($filteredDivisions, $line, $filteredProducts));
        }
        return $data;
    }

    /**
     * Retrieves all divisions for selected users across specified lines.
     * Used when filtering by users.
     *
     * @param array $lineIds Array of line IDs.
     * @param array $userIds Array of user IDs.
     * @return Collection Collection of LineDivision models.
     */
    private function getAllDivisionsForSelectedUsers(array $lineIds, array $userIds): Collection
    {
        $divisions = LineDivisionUser::whereIn('line_id', $lineIds)->whereIn('user_id', $userIds)
            ->where('from_date', '<=', $this->to)
            ->where(fn($q) => $q->where('to_date', '>=', $this->from)->orWhereNull('to_date'))
            ->with(['linedivision' => fn($q) => $q->where('is_kol', 0)])->get()
            ->map(fn($ldu) => $ldu->linedivision)->filter()->unique('id')->values();
        $this->filters['divisions'] = $divisions->pluck('id')->toArray(); // Update filters
        return $divisions;
    }

    /**
     * Gets filtered line division objects based on the current filter settings.
     *
     * @param Line $line The current line being processed.
     * @param Collection $preloadedDivisions Preloaded divisions for the current line.
     * @param Collection $userSpecificDivisions Preloaded divisions specific to selected users (if any).
     * @return Collection Filtered collection of LineDivision objects.
     */
    private function getFilteredObjects(Line $line, Collection $preloadedDivisions, Collection $userSpecificDivisions): Collection
    {
        $divisions = match ($this->perDivOrUserFilter) {
            1 => $preloadedDivisions->when(!empty($this->divisionIds), fn($c) => $c->whereIn("id", $this->divisionIds)),
            2 => (function () use ($preloadedDivisions, $userSpecificDivisions) {
                $currentLineDivisionIds = $preloadedDivisions->pluck('id')->all();
                return empty($this->userIds) ? $preloadedDivisions : $userSpecificDivisions->filter(fn($d) => in_array($d->id, $currentLineDivisionIds));
            })(),
            default => $preloadedDivisions,
        };

        // Apply authUser's visibility filter on divisions
        return $this->authUser->filterDivisions($line, $divisions, $this->filters, $this->from, $this->to)->values();
    }

    /**
     * Retrieves filtered products for a given line using preloaded product data.
     * Product filtering strategy is determined by $this->type.
     * Results are cached.
     *
     * @param Line $line The current line object.
     * @param Collection $preloadedProducts Preloaded products for the current line.
     * @return Collection Filtered collection of products.
     */
    private function getFilteredProducts(Line $line, Collection $preloadedProducts): Collection
    {
        // The cache key should ideally use $this->type if it influences the query inside strategies,
        // but strategies currently use $this->productIds passed at construction.
        // If strategies were to use $this->type directly, it should be part of cache key.
        $cacheKey = $this->generateCacheKey("filtered_products", [$line->id, (string)($this->type ?? 'default'), $this->from->toDateString(), $this->to->toDateString(), implode('_', $this->productIds ?? [])]);

        // This cache is for the *collection-filtered* list of products.
        // The base query for $preloadedProducts (LineProduct) is already done.
        return Cache::remember($cacheKey, $this->cacheTimeout, function () use ($preloadedProducts, $line) {
            // Here, we are applying collection filters, not new DB queries.
            // The strategies created by getProductFilteringStrategy expect a QueryBuilder for DB operations.
            // This needs to be reconciled. If strategies operate on collections, their interface and implementation must change.
            // Assuming filterProductsByType was meant to work on a QueryBuilder initially:
            // For now, this will re-query based on LineProduct if strategies expect QueryBuilder.
            // This part has been refactored to use strategies on a QueryBuilder.
            // The $preloadedProducts argument to this function might be misleading if strategies re-query.
            // Let's assume getProductFilteringStrategy and filterProductsByType correctly handle the QueryBuilder.
            // The call from processLines() passes $line->products which is a Collection.
            // This indicates a mismatch: getFilteredProducts expects a Collection ($preloadedProducts)
            // but filterProductsByType (which it calls via match) expects a QueryBuilder.
            // This needs to be harmonized.

            // Corrected approach: The strategies operate on QueryBuilder.
            // So, getFilteredProducts should prepare the initial QueryBuilder.
            $query = LineProduct::query()->where('line_id', $line->id); // Base query for products of the current line
            $query = $this->applyDateRangeFilter($query, $this->from, $this->to, 'line_products');

            $strategy = $this->getProductFilteringStrategy((int)$this->type); // Ensure $this->type is int
            return $strategy->filter($query);
        });
    }

    /**
     * Processes a collection of filtered objects (divisions/users) to calculate incentives.
     *
     * @param Collection $filtered The collection of filtered LineDivision objects.
     * @param Line $line The current line being processed.
     * @param Collection $products The collection of relevant products.
     * @return Collection Collection of data items with calculated incentives.
     */
    private function processFilteredObjects(Collection $filtered, Line $line, Collection $products): Collection
    {
        $data = new Collection([]);
        $chunkedFiltered = $filtered->filter(fn($division) => !$division->is_kol)->chunk(100); // Process in chunks for memory efficiency
        foreach ($chunkedFiltered as $chunk) {
            $chunkData = $chunk->map(fn($object) => $this->processObject($object, $line, $products))->collapse();
            $data = $data->merge($chunkData);
        }
        $this->processLegacyIncentives($data, $products); // Apply legacy and linked position adjustments
        return $data;
    }

    /**
     * Applies legacy incentive calculations and linked position processing.
     * Uses strategy pattern for different legacy calculation passes and delegates linked position
     * logic to LinkedPositionIncentiveService.
     *
     * @param Collection $data The main data collection (passed by reference).
     * @return void
     */
    private function processLegacyIncentives(Collection &$data, Collection $productLines): void
    {
        $trackedIncentives = $this->salesIncentivesHolder->getTrackedManagerIncentives();
        foreach ($trackedIncentives as $id => $managersIncentiveEntry) {
            $this->districtLegacyStrategy->process($data, $this->salesIncentivesHolder, $managersIncentiveEntry, (int)$id);
        }
        $trackedIncentives = $this->salesIncentivesHolder->getTrackedManagerIncentives();
        foreach ($trackedIncentives as $id => $managersIncentiveEntry) {
            $this->areaLegacyStrategy->process($data, $this->salesIncentivesHolder, $managersIncentiveEntry, (int)$id);
        }
        $trackedIncentives = $this->salesIncentivesHolder->getTrackedManagerIncentives();
        foreach ($trackedIncentives as $id => $managersIncentiveEntry) {
            $this->countryLegacyStrategy->process($data, $this->salesIncentivesHolder, $managersIncentiveEntry, (int)$id);
        }

        // Corrected: Removed $this->authUser as it's not part of LinkedPositionIncentiveService::process signature
        $this->linkedPositionIncentiveService->process(
            $data,
            $productLines,
            $this->salesIncentivesHolder,
            $this->lineIds,
            $this->from,
            $this->to
        );
    }

    /**
     * Processes a single object (LineDivision) to generate incentive view data.
     * Delegates to either sales view or monthly view report service based on $this->view.
     *
     * @param LineDivision $object The LineDivision object to process.
     * @param Line $line The current line.
     * @param Collection $products The collection of relevant products.
     * @return Collection Collection of view data items.
     */
    private function processObject(LineDivision $object, Line $line, Collection $products): Collection
    {
        return match ($this->view) {
            IncentiveReportTypes::SALES_VIEW => $this->salesViewReportService->salesView($object, $line, $products,true),
            IncentiveReportTypes::MONTHS_VIEW => $this->salesMonthlyViewReportService->monthView($object, $line, $products),
        };
    }

    /**
     * Prepares the response data by ensuring uniqueness based on filter type.
     *
     * @param Collection $data The processed incentive data.
     * @return Collection The data with unique items.
     */
    public function responseData(Collection $data): Collection
    {
        if ($this->perDivOrUserFilter == 2) { // User filter
            return $data->unique(fn($item) => ($item['id'] ?? '') . ($item['line'] ?? '') . ($item['employee'] ?? '') . ($item['product'] ?? ''));
        } else { // Division filter
            if (!$this->isChecked) { // Division total view
                return $data->unique(fn($item) => ($item['id'] ?? '') . ($item['line'] ?? '') . ($item['division'] ?? '') . ($item['product'] ?? ''));
            } else { // Brick view (isChecked implies brick level)
                return $data->unique(fn($item) => ($item['id'] ?? '') . ($item['line'] ?? '') . ($item['brick'] ?? '') . ($item['product'] ?? ''));
            }
        }
    }
}
