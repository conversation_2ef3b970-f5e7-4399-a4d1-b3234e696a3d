<?php

namespace App\Services\Reports;

use App\Account;
use App\ActualVisit;
use App\CallRate;
use App\Doctor;
use App\Line;
use App\LineDivision;
use App\Sale;
use App\SaleDetail;
use App\TargetDetails;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Service for generating helicopter view reports with comprehensive metrics
 *
 * Provides total accounts, visit coverage, call rate, frequency, sales quantities,
 * and targets for specified line divisions within a date range.
 */
class HelicopterViewReportService
{
    /**
     * Generate helicopter view report for given parameters
     *
     * @param Carbon $fromDate Start date for the report
     * @param Carbon $toDate End date for the report
     * @param array $lineIds Array of line IDs to include in the report
     * @param User|null $user User context for filtering (defaults to authenticated user)
     * @return array Comprehensive report data
     */
    public function generateReport(Carbon $fromDate, Carbon $toDate, array $lineIds, ?User $user = null): array
    {
        $user = $user ?? Auth::user();

        Log::info('Generating helicopter view report', [
            'from_date' => $fromDate->toDateString(),
            'to_date' => $toDate->toDateString(),
            'line_ids' => $lineIds,
            'user_id' => $user->id
        ]);

        // Validate line access for user
        $accessibleLines = $this->getAccessibleLines($user, $lineIds, $fromDate, $toDate);

        if ($accessibleLines->isEmpty()) {
            Log::warning('No accessible lines found for user', [
                'user_id' => $user->id,
                'requested_line_ids' => $lineIds
            ]);
            return $this->getEmptyReport();
        }

        $actualLineIds = $accessibleLines->pluck('id')->toArray();

        // Get line divisions for the accessible lines
        $lineDivisions = $this->getLineDivisions($actualLineIds, $fromDate, $toDate);

        $report = [
            'period' => [
                'from_date' => $fromDate->toDateString(),
                'to_date' => $toDate->toDateString()
            ],
            'lines' => $accessibleLines->map(fn($line) => [
                'id' => $line->id,
                'name' => $line->name
            ])->toArray(),
            'metrics' => $this->getMetricsPerLine($accessibleLines, $lineDivisions, $fromDate, $toDate)
        ];

        Log::info('Helicopter view report generated successfully', [
            'metrics_count' => count($report['metrics']),
            'lines_count' => count($report['lines'])
        ]);

        return $report;
    }

    /**
     * Get lines accessible to the user from the requested line IDs
     */
    private function getAccessibleLines(User $user, array $lineIds, Carbon $fromDate, Carbon $toDate): Collection
    {
        // Admin users can access all lines
        if ($user->hasRole('admin') || $user->hasRole('sub admin') || $user->hasRole('Gemstone Admin')) {
            return Line::whereIn('id', $lineIds)
                ->where('from_date', '<=', $toDate)
                ->where(fn($q) => $q->where('to_date', '>', $fromDate)->orWhere('to_date', null))
                ->select('id', 'name')
                ->get();
        }

        // Regular users can only access their assigned lines
        return $user->lines($fromDate, $toDate)
            ->whereIn('lines.id', $lineIds)
            ->select('lines.id', 'lines.name')
            ->get();
    }

    /**
     * Get line divisions for the specified lines and date range
     */
    private function getLineDivisions(array $lineIds, Carbon $fromDate, Carbon $toDate): Collection
    {
        return LineDivision::whereIn('line_id', $lineIds)
            ->where('from_date', '<=', $toDate)
            ->where(fn($q) => $q->where('to_date', '>', $fromDate)->orWhere('to_date', null))
            ->select('id', 'line_id', 'name', 'division_type_id')
            ->get();
    }

    /**
     * Get metrics per line
     */
    private function getMetricsPerLine(Collection $lines, Collection $lineDivisions, Carbon $fromDate, Carbon $toDate): array
    {
        $lineIds = $lines->pluck('id')->toArray();
        if (empty($lineIds)) {
            return [];
        }

        try {
            $totalAccounts = $this->getTotalAccountsForLines($lineIds, $fromDate, $toDate);
            $totalVisitCoverage = $this->getTotalVisitCoverageForLines($lineIds, $fromDate, $toDate);
            $totalCallRate = $this->getTotalCallRateForLines($lineDivisions, $fromDate, $toDate);
            $totalFrequency = $this->getTotalFrequencyForLines($lineIds, $fromDate, $toDate);
            $totalSalesQuantities = $this->getTotalSalesQuantitiesForLines($lineIds, $fromDate, $toDate);
            $totalTargetsUnits = $this->getTotalTargetsUnitsForLines($lineDivisions, $fromDate, $toDate);

            return $lines->map(function ($line) use ($totalAccounts, $totalVisitCoverage, $totalCallRate, $totalFrequency, $totalSalesQuantities, $totalTargetsUnits) {
                $lineId = $line->id;
                $metrics = [
                    'line_id' => $lineId,
                    'line_name' => $line->name,
                    'total_accounts' => $totalAccounts->get($lineId, 0),
                    'total_visit_coverage' => $totalVisitCoverage->get($lineId, 0),
                    'total_call_rate' => $totalCallRate->get($lineId, 0.0),
                    'total_frequency' => $totalFrequency->get($lineId, 0.0),
                    'total_sales_quantities' => $totalSalesQuantities->get($lineId, 0),
                    'total_targets_units' => $totalTargetsUnits->get($lineId, 0)
                ];

                Log::debug('Calculated metrics for line', [
                    'line_id' => $lineId,
                    'metrics' => $metrics
                ]);

                return $metrics;
            })->toArray();
        } catch (\Exception $e) {
            Log::error('Error calculating metrics for lines', [
                'line_ids' => $lineIds,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return default metrics on error for all lines
            return $lines->map(function ($line) {
                return [
                    'line_id' => $line->id,
                    'line_name' => $line->name,
                    'total_accounts' => 0,
                    'total_visit_coverage' => 0,
                    'total_call_rate' => 0.0,
                    'total_frequency' => 0.0,
                    'total_sales_quantities' => 0,
                    'total_targets_units' => 0
                ];
            })->toArray();
        }
    }

    /**
     * Calculate total accounts for given lines
     */
    private function getTotalAccountsForLines(array $lineIds, Carbon $fromDate, Carbon $toDate): Collection
    {
        return Account::query()
            ->join("account_lines", "accounts.id", "=", "account_lines.account_id")
            ->whereIn("account_lines.line_id", $lineIds)
            ->where("account_lines.from_date", "<=", $toDate)
            ->where(function ($q) use ($fromDate) {
                $q->where("account_lines.to_date", ">", $fromDate)->orWhereNull(
                    "account_lines.to_date"
                );
            })
            ->where(function ($q) use ($toDate) {
                $q->where("accounts.active_date", "<=", $toDate)->orWhere(
                    "active_date",
                    null
                );
            })
            ->where(function ($q) use ($fromDate) {
                $q->where("accounts.inactive_date", ">", $fromDate)->orWhere(
                    "inactive_date",
                    null
                );
            })
            ->select(
                "account_lines.line_id",
                DB::raw("count(distinct crm_accounts.id) as total")
            )
            ->groupBy("account_lines.line_id")
            ->pluck('total', 'line_id');
    }

    /**
     * Calculate total visit coverage for given lines
     */
    private function getTotalVisitCoverageForLines(array $lineIds, Carbon $fromDate, Carbon $toDate): Collection
    {
        return ActualVisit::whereIn('line_id', $lineIds)
            ->whereBetween('visit_date', [$fromDate, $toDate])
            ->select('line_id', DB::raw('count(distinct account_id) as total'))
            ->groupBy('line_id')
            ->pluck('total', 'line_id');
    }

    /**
     * Calculate total call rate for a set of line divisions
     */
    private function getTotalCallRateForLines(Collection $lineDivisions, Carbon $fromDate, Carbon $toDate): Collection
    {
        if ($lineDivisions->isEmpty()) {
            return collect();
        }

        $divisionIds = $lineDivisions->pluck('id')->toArray();
        $divisionToLineMap = $lineDivisions->pluck('line_id', 'id');

        $callRates = CallRate::whereIn('division_id', $divisionIds)
            ->whereBetween('date', [$fromDate, $toDate])
            ->select('division_id', 'call_rate')
            ->get();

        return $callRates->groupBy(function ($rate) use ($divisionToLineMap) {
            return $divisionToLineMap[$rate->division_id] ?? null;
        })->map(function ($rates) {
            return round($rates->avg('call_rate'), 2);
        });
    }

    /**
     * Calculate total frequency for given lines
     */
    private function getTotalFrequencyForLines(array $lineIds, Carbon $fromDate, Carbon $toDate): Collection
    {
        $visits = ActualVisit::whereIn('line_id', $lineIds)
            ->whereBetween('visit_date', [$fromDate, $toDate])
            ->whereNotNull('account_dr_id')
            ->select('line_id', 'account_dr_id')
            ->get();

        return $visits->groupBy('line_id')->map(function ($lineVisits) {
            $totalVisits = $lineVisits->count();
            $uniqueDoctors = $lineVisits->unique('account_dr_id')->count();

            return $uniqueDoctors > 0 ? round($totalVisits / $uniqueDoctors, 2) : 0.0;
        });
    }

    /**
     * Calculate total sales quantities for given lines
     */
    private function getTotalSalesQuantitiesForLines(array $lineIds, Carbon $fromDate, Carbon $toDate): Collection
    {
        return SaleDetail::whereIn("line_id", $lineIds)
            ->whereBetween('date', [$fromDate, $toDate])
            ->groupBy('line_id')
            ->selectRaw('line_id, sum(quantity) as total_quantity')
            ->pluck('total_quantity', 'line_id');
    }

    /**
     * Calculate total target units for a set of line divisions
     */
    private function getTotalTargetsUnitsForLines(Collection $lineDivisions, Carbon $fromDate, Carbon $toDate): Collection
    {
        if ($lineDivisions->isEmpty()) {
            return collect();
        }

        $lineIds = $lineDivisions->pluck('line_id')->unique()->toArray();
        $divisionIds = $lineDivisions->pluck('id')->toArray();

        return TargetDetails::whereIn('line_id', $lineIds)
            ->whereIn('div_id', $divisionIds)
            ->whereBetween('date', [$fromDate, $toDate])
            ->groupBy('line_id')
            ->selectRaw('line_id, sum(target) as total_target')
            ->pluck('total_target', 'line_id');
    }

    /**
     * Get empty report structure
     */
    private function getEmptyReport(): array
    {
        return [
            'period' => [
                'from_date' => null,
                'to_date' => null
            ],
            'lines' => [],
            'metrics' => []
        ];
    }
}
