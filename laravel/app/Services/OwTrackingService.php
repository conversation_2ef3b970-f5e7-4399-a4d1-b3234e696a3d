<?php

namespace App\Services;

use App\OwActualVisit;
use App\Shift;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class OwTrackingService
{
    private const CACHE_TTL = 1800; // 30 minutes

    /**
     * Check if user has OW for a specific date and determine which shifts are affected
     * 
     * @param User $user
     * @param string $date
     * @param int|null $shiftId
     * @return array ['hasOw' => bool, 'affectedShifts' => array, 'owType' => string]
     */
    public function getUserOwStatus(User $user, string $date, ?int $shiftId = null): array
    {
        $targetDate = Carbon::parse($date);
        $cacheKey = "ow_status_{$user->id}_{$targetDate->format('Y-m-d')}";
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($user, $targetDate, $shiftId) {
            return $this->calculateOwStatus($user, $targetDate, $shiftId);
        });
    }

    /**
     * Get OW status for multiple users and dates (bulk operation)
     * 
     * @param Collection $users
     * @param array $dates
     * @return array
     */
    public function getBulkOwStatus(Collection $users, array $dates): array
    {
        $results = [];
        
        // Get all OW records for the users and date range in one query
        $userIds = $users->pluck('id')->toArray();
        $fromDate = min($dates);
        $toDate = max($dates);
        
        $owRecords = $this->getBulkOwRecords($userIds, $fromDate, $toDate);
        
        foreach ($users as $user) {
            $userOwRecords = $owRecords->where('user_id', $user->id);
            $results[$user->id] = [];
            
            foreach ($dates as $date) {
                $results[$user->id][$date] = $this->calculateOwStatusFromCollection(
                    $userOwRecords, 
                    Carbon::parse($date)
                );
            }
        }
        
        return $results;
    }

    /**
     * Enhanced hasOw method that handles AM/PM and full-day logic
     * 
     * @param User $user
     * @param string $date
     * @param int|null $shiftId
     * @return bool
     */
    public function hasOw(User $user, string $date, ?int $shiftId = null): bool
    {
        $owStatus = $this->getUserOwStatus($user, $date, $shiftId);
        
        if (!$owStatus['hasOw']) {
            return false;
        }
        
        // If no specific shift requested, return true if any OW exists
        if ($shiftId === null) {
            return true;
        }
        
        // Check if the specific shift is affected
        return in_array($shiftId, $owStatus['affectedShifts']) || 
               $owStatus['owType'] === 'full_day';
    }

    /**
     * Get all shifts that should show OW for a user on a specific date
     * 
     * @param User $user
     * @param string $date
     * @return array
     */
    public function getOwAffectedShifts(User $user, string $date): array
    {
        $owStatus = $this->getUserOwStatus($user, $date);
        
        if (!$owStatus['hasOw']) {
            return [];
        }
        
        // If full day OW or has both AM and PM, return all shifts
        if ($owStatus['owType'] === 'full_day' || $owStatus['owType'] === 'both_shifts_same_day') {
            return $this->getAllShiftIds();
        }
        
        return $owStatus['affectedShifts'];
    }

    /**
     * Calculate OW status for a user on a specific date
     * 
     * @param User $user
     * @param Carbon $targetDate
     * @param int|null $shiftId
     * @return array
     */
    private function calculateOwStatus(User $user, Carbon $targetDate, ?int $shiftId = null): array
    {
        $owRecords = OwActualVisit::where('user_id', $user->id)
            ->whereDate('date', $targetDate->toDateString())
            ->get();

        return $this->calculateOwStatusFromCollection($owRecords, $targetDate);
    }

    /**
     * Calculate OW status from a collection of OW records
     * 
     * @param Collection $owRecords
     * @param Carbon $targetDate
     * @return array
     */
    private function calculateOwStatusFromCollection(Collection $owRecords, Carbon $targetDate): array
    {
        if ($owRecords->isEmpty()) {
            return [
                'hasOw' => false,
                'affectedShifts' => [],
                'owType' => 'none'
            ];
        }

        $affectedShifts = [];
        $hasFullDay = false;
        $sameDayOwRecords = [];

        foreach ($owRecords as $owRecord) {
            // Check if OW record is for the target date
            if (!Carbon::parse($owRecord->date)->isSameDay($targetDate)) {
                continue;
            }

            // Full day OW (shift_id is null)
            if ($owRecord->shift_id === null) {
                $hasFullDay = true;
                break;
            }

            // Collect same-day shift-specific OW records
            $sameDayOwRecords[] = $owRecord;

            // Specific shift OW
            if (!in_array($owRecord->shift_id, $affectedShifts)) {
                $affectedShifts[] = $owRecord->shift_id;
            }
        }

        // Enhanced same-day OW detection
        $hasBothAmPmOnSameDay = $this->checkForAmPmOwOnSameDay($sameDayOwRecords, $targetDate);

        // Determine OW type with enhanced same-day logic
        $owType = 'none';
        if ($hasFullDay) {
            $owType = 'full_day';
        } elseif ($hasBothAmPmOnSameDay) {
            // Has both AM and PM OW on the SAME DAY
            $owType = 'both_shifts_same_day';
            // Treat as full day - affect all shifts
            $affectedShifts = $this->getAllShiftIds();
        } elseif (count($affectedShifts) >= 2) {
            // Has multiple shifts
            $owType = 'multiple_shifts';
        } elseif (count($affectedShifts) === 1) {
            $owType = 'single_shift';
        }

        return [
            'hasOw' => $hasFullDay || !empty($affectedShifts),
            'affectedShifts' => $hasFullDay || $hasBothAmPmOnSameDay ? $this->getAllShiftIds() : $affectedShifts,
            'owType' => $owType
        ];
    }

    /**
     * Check if user has both AM and PM OW on the same specific day
     *
     * @param array $sameDayOwRecords
     * @param Carbon $targetDate
     * @return bool
     */
    private function checkForAmPmOwOnSameDay(array $sameDayOwRecords, Carbon $targetDate): bool
    {
        $shiftsOnTargetDate = [];

        foreach ($sameDayOwRecords as $owRecord) {
            // Check if the OW record is specifically for the target date
            if (Carbon::parse($owRecord->date)->isSameDay($targetDate)) {
                if ($owRecord->shift_id !== null) {
                    $shiftsOnTargetDate[] = $owRecord->shift_id;
                }
            }
        }

        // Remove duplicates and check if we have both AM (1) and PM (2) shifts
        $uniqueShifts = array_unique($shiftsOnTargetDate);

        // Check for common AM/PM shift combinations
        // Assuming shift 1 = AM, shift 2 = PM (adjust based on your system)
        return in_array(1, $uniqueShifts) && in_array(2, $uniqueShifts);
    }

    /**
     * Get bulk OW records for multiple users and date range
     * 
     * @param array $userIds
     * @param string $fromDate
     * @param string $toDate
     * @return Collection
     */
    private function getBulkOwRecords(array $userIds, string $fromDate, string $toDate): Collection
    {
        $cacheKey = "bulk_ow_records_" . md5(serialize($userIds) . $fromDate . $toDate);
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($userIds, $fromDate, $toDate) {
            return OwActualVisit::whereIn('user_id', $userIds)
                ->whereBetween('date', [$fromDate, $toDate])
                ->get();
        });
    }

    /**
     * Get all available shift IDs
     * 
     * @return array
     */
    private function getAllShiftIds(): array
    {
        return Cache::remember('all_shift_ids', self::CACHE_TTL, function () {
            return Shift::pluck('id')->toArray();
        });
    }

    /**
     * Get OW summary for reporting
     * 
     * @param User $user
     * @param string $date
     * @return array
     */
    public function getOwSummary(User $user, string $date): array
    {
        $owStatus = $this->getUserOwStatus($user, $date);
        
        if (!$owStatus['hasOw']) {
            return [
                'status' => 'no_ow',
                'display' => '',
                'color' => '',
                'affected_shifts' => []
            ];
        }

        $display = 'ow';
        $color = 'green';

        switch ($owStatus['owType']) {
            case 'full_day':
                $display = 'ow';
                break;
            case 'both_shifts_same_day':
                $display = 'ow';
                break;
            case 'both_shifts':
            case 'multiple_shifts':
                $display = 'ow';
                break;
            case 'single_shift':
                $display = 'ow';
                break;
        }

        return [
            'status' => 'ow',
            'display' => $display,
            'color' => $color,
            'affected_shifts' => $owStatus['affectedShifts']
        ];
    }
}
