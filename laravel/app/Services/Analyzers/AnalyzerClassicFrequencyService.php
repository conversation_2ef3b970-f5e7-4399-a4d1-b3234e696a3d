<?php

namespace App\Services\Analyzers;

use App\Exceptions\CrmException;
use Illuminate\Support\Collection;

class AnalyzerClassicFrequencyService
{
    /**
     * @var array<string, array<string, int>>
     */
    private array $frequency = [];
    private array $colorMap = [];
    private array $palette = [
        ['primary' => '#00bcd4', 'accent' => '#80deea', 'above' => '#00838f'], // cyan
        ['primary' => '#2196f3', 'accent' => '#90caf9', 'above' => '#1976d2'], // blue
        ['primary' => '#f44336', 'accent' => '#ef9a9a', 'above' => '#c62828'], // red
        ['primary' => '#ff9800', 'accent' => '#ffcc80', 'above' => '#ef6c00'], // orange
        ['primary' => '#9c27b0', 'accent' => '#ce93d8', 'above' => '#6a1b9a'], // purple
        ['primary' => '#4caf50', 'accent' => '#a5d6a7', 'above' => '#2e7d32'], // green
        // Add more if needed
    ];
    /**
     * @var \Illuminate\Support\Collection|null
     */
    private ?Collection $classes = null;

    public function __construct(Collection $classes)
    {
        $this->classes = $classes;
        $this->initFrequency();
        $this->initColorMap();
    }
    private function initFrequency(): void
    {
        foreach ($this->classes as $class) {
            $this->frequency[$class] = [
                'meet' => 0,
                'below' => 0,
                'above' => 0
            ];
        }
    }

    private function initColorMap(): void
    {
        foreach ($this->classes as $index => $class) {
            $this->colorMap[$class] = $this->palette[$index % count($this->palette)];
        }
    }

    public function setClasses(Collection $classes): void
    {
        $this->classes = $classes;
    }

    public function set(string $level, int $meet, int $below, int $above): void
    {
        if (isset($this->frequency[$level])) {
            $this->frequency[$level]['meet'] = $meet;
            $this->frequency[$level]['below'] = $below;
            $this->frequency[$level]['above'] = $above;
        }
    }

    public function addTo(string $level, int $meet = 0, int $below = 0, int $above = 0): void
    {
        if (isset($this->frequency[$level])) {
            $this->frequency[$level]['meet'] += $meet;
            $this->frequency[$level]['below'] += $below;
            $this->frequency[$level]['above'] += $above;
        }
    }

    public function get(string $level): array
    {
        return $this->frequency[$level] ?? ['meet' => 0, 'below' => 0, 'above' => 0];
    }

    public function all(): array
    {
        $levels = $this->classes;
        $colorMap = [];
        $palette = [
            ['primary' => '#2196f3', 'accent' => '#90caf9', 'above' => '#1976d2'], // blue
            ['primary' => '#4caf50', 'accent' => '#a5d6a7', 'above' => '#2e7d32'], // green
            ['primary' => '#ff9800', 'accent' => '#ffcc80', 'above' => '#ef6c00'], // orange
            ['primary' => '#00bcd4', 'accent' => '#80deea', 'above' => '#00838f'], // cyan
            ['primary' => '#f44336', 'accent' => '#ef9a9a', 'above' => '#c62828'], // red
            ['primary' => '#9c27b0', 'accent' => '#ce93d8', 'above' => '#6a1b9a'], // purple
            // Add more if needed
        ];
        foreach ($levels as $index => $class) {
            $colorMap[$class] = $palette[$index % count($palette)];
        }

        $result = [];

        foreach ($levels as $level) {
            $result[] = [
                'name' => $level,
                'meet' => [
                    'value' => $this->frequency[$level]['meet'] ?? 0,
                    'color' => $colorMap[$level]['primary'] ?? '#ccc'
                ],
                'below' => [
                    'value' => $this->frequency[$level]['below'] ?? 0,
                    'color' => $colorMap[$level]['accent'] ?? '#eee'
                ],
                'above' => [
                    'value' => $this->frequency[$level]['above'] ?? 0,
                    'color' => $colorMap[$level]['above'] ?? '#aaa'
                ]
            ];
        }

        return $result;
    }
}
