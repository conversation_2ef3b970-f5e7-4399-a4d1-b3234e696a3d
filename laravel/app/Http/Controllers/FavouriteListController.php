<?php

namespace App\Http\Controllers;

use App\Account;
use App\AccountLines;
use App\AccountType;
use App\Brick;
use App\DivisionType;
use App\Doctor;
use App\Exceptions\CrmException;
use App\Helpers\LogActivity;
use App\Helpers\Notifications\NotificationHelper;
use App\Http\Requests\ApprovalEmployeesRequest;
use App\Http\Requests\FavouriteListRequest;
use App\Line;
use App\LineBricks;
use App\LineDivisionUser;
use App\Models\ApprovalFlowUser;
use App\Models\ApprovalSetting;
use App\Models\ListSetting;
use App\Models\ListType;
use App\Models\NewAccountDoctor;
use App\Notifications\ActiveInactiveApprovedNotification;
use App\PlanVisitDetails;
use App\Position;
use App\Reason;
use App\Services\ListService;
use App\User;
use Carbon\CarbonPeriod;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class FavouriteListController extends ApiController
{
    private function getActiveAccountDoctorsSubQuery($listFilter)
    {
        return DB::table('new_account_doctors as nad1')
            ->select('nad1.id', 'nad1.account_id', 'nad1.account_lines_id')
            ->whereNull('nad1.deleted_at')
            ->where('nad1.line_id', $listFilter['line_id'])
            ->where('nad1.from_date', '<=', Carbon::now())
            ->where(function ($query) {
                $query->whereNull('nad1.to_date')
                    ->orWhere('nad1.to_date', '>=', Carbon::now());
            })
            ->groupBy('nad1.id', 'nad1.account_id', 'nad1.account_lines_id');
    }

    private function getInactiveAccountDoctorsSubQuery($listFilter, $doctors)
    {
        return DB::table('new_account_doctors as nad1')
            ->select('nad1.id', 'nad1.account_id', 'nad1.account_lines_id')
            ->whereNull('nad1.deleted_at')
            ->whereNotIn('nad1.doctor_id', $doctors)
            ->where('nad1.line_id', $listFilter['line_id'])
            ->where(fn($q) => $q->where('nad1.from_date', '>', (string)Carbon::now())
                ->orWhere('nad1.to_date', '<=', (string)Carbon::now()))
            ->groupBy('nad1.id', 'nad1.account_id', 'nad1.account_lines_id');
    }
    private function getActive($listFilter, $user)
    {
        $latestNewAccountDoctors = $this->getActiveAccountDoctorsSubQuery($listFilter);
        $activeAccounts = Account::select(
            'accounts.id as id',
            'accounts.id as account_id',
            'lines.name as line',
            'lines.id as line_id',
            'line_divisions.name as division',
            'line_divisions.id as div_id',
            DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
            DB::raw('IFNULL(group_concat(distinct crm_bricks.id),"") as brick_id'),
            DB::raw('IFNULL(crm_accounts.code,"") as acc_code'),
            DB::raw('IFNULL(crm_accounts.name,"") as account'),
            DB::raw('IFNULL(crm_accounts.tel,"") as tel'),
            DB::raw('IFNULL(crm_accounts.email,"") as email'),
            DB::raw('IFNULL(crm_accounts.address,"") as address'),
            DB::raw('IFNULL(crm_a.name,"") as acc_class'),
            'account_types.name as account_type',
            DB::raw('IFNULL(crm_doctors.name,"") as doctor'),
            DB::raw('IFNULL(crm_doctors.ucode,"") as ucode'),
            DB::raw('IFNULL(crm_doctors.id,"") as doctor_id'),
            DB::raw('IFNULL(crm_d.name,"") as doc_class'),
            'new_account_doctors.from_date',
            'new_account_doctors.to_date',
            DB::raw('Count(distinct crm_actual_visits.id) as no_visits'),
            'specialities.name as speciality',
            'new_account_doctors.id as acc_dr_id',
            'new_account_doctors.account_lines_id as acc_line_id',
            DB::raw("NULL as reason_id"),
            DB::raw('DATE_FORMAT(crm_new_account_doctors.from_date,"%d-%m-%Y") as from_date'),
            DB::raw('IFNULL(DATE_FORMAT(crm_new_account_doctors.to_date,"%d-%m-%Y"),"") as to_date'),
            'division_types.color'
        )
            ->join('account_types', function ($join) use ($listFilter) {
                $join->on('accounts.type_id', 'account_types.id')
                    ->whereIntegerInRaw('accounts.type_id', $listFilter['type_id']);
            })
            ->join(
                'account_lines',
                function ($join) use ($listFilter) {
                    $join->on('accounts.id', 'account_lines.account_id')
                        ->where('account_lines.line_id', $listFilter['line_id'])
                        ->whereNull('account_lines.deleted_at')
                        ->whereIn('account_lines.line_division_id', $listFilter['div_id'])
                        ->where(fn($q) => $q->where('account_lines.to_date', '=', null)
                            ->orWhere('account_lines.to_date', '>=', (string)Carbon::now()));
                    if (!empty($listFilter['brick_id'])) {
                        $join->whereIntegerInRaw('account_lines.brick_id', $listFilter['brick_id']);
                    }
                    if (!empty($listFilter['class_id'])) {
                        $join->where(fn($q) => $q->whereIntegerInRaw('account_lines.class_id', $listFilter['class_id'])
                            ->orWhereNull('account_lines.class_id'));
                    }
                }
            )
            ->joinSub($latestNewAccountDoctors, 'latest_new_account_doctors', function ($join) {
                $join->on('accounts.id', '=', 'latest_new_account_doctors.account_id')
                    ->on('account_lines.id', '=', 'latest_new_account_doctors.account_lines_id');
            })
            ->join('new_account_doctors', function ($join) {
                $join->on('new_account_doctors.id', '=', 'latest_new_account_doctors.id');
            })
            ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
            ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->leftJoin('actual_visits', function ($join) use ($user) {
                $join->on('accounts.id', 'actual_visits.account_id')
                    ->where('actual_visits.user_id', $user->id)
                    ->whereBetween('actual_visits.visit_date', [Carbon::now()->firstOfMonth(), Carbon::now()->endOfMonth()]);
            })
            ->join('doctors', function ($join) use ($listFilter) {
                $join->on('new_account_doctors.doctor_id', 'doctors.id')
                    ->whereNull('doctors.deleted_at')
                    ->whereIntegerInRaw('doctors.speciality_id', $listFilter['speciality_id'])
                    ->whereIn('new_account_doctors.id', function ($q) {
                        $q->selectRaw("MAX(crm_new_account_doctors.id)")
                            ->groupBy("doctors.id");
                    });
            })
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('classes as d', 'doctors.class_id', 'd.id')
            ->leftJoin('classes as a', 'account_lines.class_id', 'a.id')
            ->where(fn($q) => $q->where('accounts.inactive_date', '=', null)->orWhere('accounts.inactive_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->where('doctors.inactive_date', '=', null)->orWhere('doctors.inactive_date', '>=', (string)Carbon::now()))
            ->where('accounts.active_date', '<=', Carbon::now())
            ->where('doctors.active_date', '<=', Carbon::now())
            ->where('accounts.deleted_at', null)
            ->orderBy('doctors.ucode', 'asc');

        $activeAccounts = $activeAccounts->groupBy(
            "accounts.id",
            "doctors.id",
            "lines.id",
            "line_divisions.id",
            "a.name",
            "new_account_doctors.id",
            "new_account_doctors.from_date",
            "new_account_doctors.to_date",
            "new_account_doctors.account_lines_id"
        )->addSelect(DB::raw("true as flag"))->get();
        return $activeAccounts; 
    }

    private function getInactive($listFilter, $user, $doctors)
    {
        $latestNewAccountDoctors = $this->getInactiveAccountDoctorsSubQuery($listFilter, $doctors);
        $InactiveAccounts = Account::select(
            'accounts.id as id',
            'accounts.id as account_id',
            'lines.name as line',
            'lines.id as line_id',
            'line_divisions.name as division',
            'line_divisions.id as div_id',
            DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
            DB::raw('IFNULL(group_concat(distinct crm_bricks.id),"") as brick_id'),
            DB::raw('IFNULL(crm_accounts.code,"") as acc_code'),
            DB::raw('IFNULL(crm_accounts.name,"") as account'),
            DB::raw('IFNULL(crm_accounts.tel,"") as tel'),
            DB::raw('IFNULL(crm_accounts.email,"") as email'),
            DB::raw('IFNULL(crm_accounts.address,"") as address'),
            DB::raw('IFNULL(crm_a.name,"") as acc_class'),
            'account_types.name as account_type',
            DB::raw('IFNULL(crm_doctors.name,"") as doctor'),
            DB::raw('IFNULL(crm_doctors.ucode,"") as ucode'),
            DB::raw('IFNULL(crm_doctors.id,"") as doctor_id'),
            DB::raw('IFNULL(crm_d.name,"") as doc_class'),
            'new_account_doctors.from_date',
            'new_account_doctors.to_date',
            'specialities.name as speciality',
            DB::raw('Count(distinct crm_actual_visits.id) as no_visits'),
            'new_account_doctors.id as acc_dr_id',
            DB::raw("NULL as reason_id"),
            'new_account_doctors.account_lines_id as acc_line_id',
            DB::raw('DATE_FORMAT(crm_new_account_doctors.from_date,"%d-%m-%Y") as from_date'),
            DB::raw('IFNULL(DATE_FORMAT(crm_new_account_doctors.to_date,"%d-%m-%Y"),"") as to_date'),
            'division_types.color'
        )
            ->join('account_types', function ($join) use ($listFilter) {
                $join->on('accounts.type_id', 'account_types.id')
                    ->whereIntegerInRaw('accounts.type_id', $listFilter['type_id']);
            })
            ->join(
                'account_lines',
                function ($join) use ($listFilter) {
                    $join->on('accounts.id', 'account_lines.account_id')
                        ->where('account_lines.line_id', $listFilter['line_id'])
                        ->whereNull('account_lines.deleted_at')
                        ->whereIn('account_lines.line_division_id', $listFilter['div_id'])
                        ->where(fn($q) => $q->where('account_lines.to_date', '=', null)
                            ->orWhere('account_lines.to_date', '>=', (string)Carbon::now()));
                    if (!empty($listFilter['brick_id'])) {
                        $join->whereIntegerInRaw('account_lines.brick_id', $listFilter['brick_id']);
                    }
                    if (!empty($listFilter['class_id'])) {
                        $join->where(fn($q) => $q->whereIntegerInRaw('account_lines.class_id', $listFilter['class_id'])
                            ->orWhereNull('account_lines.class_id'));
                    }
                }
            )
            ->joinSub($latestNewAccountDoctors, 'latest_new_account_doctors', function ($join) {
                $join->on('accounts.id', '=', 'latest_new_account_doctors.account_id')
                    ->on('account_lines.id', '=', 'latest_new_account_doctors.account_lines_id');
            })
            ->join('new_account_doctors', function ($join) {
                $join->on('new_account_doctors.id', '=', 'latest_new_account_doctors.id');
            })
            ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
            ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->leftJoin('actual_visits', function ($join) use ($user) {
                $join->on('accounts.id', 'actual_visits.account_id')
                    ->where('actual_visits.user_id', $user->id)
                    ->whereBetween('actual_visits.visit_date', [Carbon::now()->firstOfMonth(), Carbon::now()->endOfMonth()]);
            })
            ->join('doctors', function ($join) use ($listFilter) {
                $join->on('new_account_doctors.doctor_id', 'doctors.id')
                    ->whereNull('doctors.deleted_at')
                    ->whereIntegerInRaw('doctors.speciality_id', $listFilter['speciality_id']);
            })
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('classes as d', 'doctors.class_id', 'd.id')
            ->leftJoin('classes as a', 'account_lines.class_id', 'a.id')
            ->where('accounts.deleted_at', null)
            ->where('accounts.hidden_fav_list', 0)
            ->orderBy('doctors.ucode', 'asc');
        $InactiveAccounts = $InactiveAccounts->groupBy(
            "accounts.id",
            "doctors.id",
            "lines.id",
            "line_divisions.id",
            "a.name",
            "new_account_doctors.id",
            "new_account_doctors.from_date",
            "new_account_doctors.to_date",
            "new_account_doctors.account_lines_id"
        )->addSelect(DB::raw("false as flag"))->get();
        return $InactiveAccounts;
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $listFilter = $request->all();
        $lists = collect([]);
        $fields = [
            "s",
            "account_id",
            "account",
            "acc_class",
            "line",
            "division",
            "brick",
            "doctor",
            "doc_class",
            "speciality",
            "no_visits",
            "from_date",
            "to_date",
            "flag",
        ];
        $user = Auth::user();
        $settingReasons = ListSetting::where('key', 'reason_inactive_list')->value('value');
        $dates[] = [
            'Month' => Carbon::now()->format('F'),
            'Year' => Carbon::now()->format('Y'),
        ];
        if ($listFilter['status'] == 2) {
            if ($settingReasons == 'Yes') $fields[] = "reason";
            $lists = $this->getActive($listFilter, $user);
        }
        if ($listFilter['status'] == 1) {
            $doctors = $this->getActive($listFilter, $user)->pluck('doctor_id')->toArray();
            $lists = $this->getInactive($listFilter, $user, $doctors);
        }
        $reasons = Reason::where('request_type', NewAccountDoctor::class)->get();
        LogActivity::addLog();
        return $this->respond(['lists' => $lists, 'fields' => $fields, 'reasons' => $reasons, 'dates' => $dates]);
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function saveWithoutApprovals($request, $parsedDate)
    {
        DB::transaction(function () use ($request, $parsedDate) {
            $now = Carbon::now();
            foreach ($request->accounts as $item) {
                $doctor = NewAccountDoctor::where('new_account_doctors.doctor_id', $item['doctor_id'])
                    ->where('new_account_doctors.account_id', $item['account_id'])
                    ->where('new_account_doctors.line_id', $item['line_id'])
                    ->where('new_account_doctors.account_lines_id', $item['acc_line_id'])
                    ->latest()->first();
                if ($request->status == 1) {
                    if ($doctor->from_date > $now) {
                        $doctor->update([
                            'from_date' => $parsedDate,
                            'to_date' => Null
                        ]);
                    }
                    if ($doctor->to_date && $doctor->to_date <= $now) {
                        NewAccountDoctor::create([
                            'account_id' => $item['account_id'],
                            'line_id' => $item['line_id'],
                            'doctor_id' => $item['doctor_id'],
                            'account_lines_id' => $item['acc_line_id'],
                            'from_date' => $parsedDate,
                            'to_date' => null,
                        ]);
                    }
                } else {
                    if (isNullable($doctor->to_date) && Carbon::parse($doctor->from_date)->toDateString() < $parsedDate) {
                        $doctor->update([
                            'to_date' => $parsedDate,
                        ]);
                    }
                }
            }
        });
    }

    public function store(FavouriteListRequest $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $accounts = $request->accounts;
        if ($request->month == null)
            throw new Exception('Invalid Month Date For Active Or Inactive Of Accounts');

        $parsedDate = null;
        if ($request->status == 2) $parsedDate = Carbon::parse($request->month)->endOfMonth()->toDateString();
        else $parsedDate = Carbon::parse($request->month)->startOfMonth()->toDateString();
        DB::transaction(function () use ($accounts, $user, $request, $parsedDate) {
            $hasApprovable = $user->division(Line::find($accounts[0]["line_id"]))->divisionType?->planable
                ->where('line_id', $accounts[0]["line_id"])->first()?->approvables()?->wherePivot('request_type', NewAccountDoctor::class)->count() > 0;
            if ($hasApprovable) {
                foreach ($accounts as $account) {
                    PlanVisitDetails::firstOrCreate([
                        'visitable_id' => $account['acc_dr_id'],
                        'account_lines_id' => $account['acc_line_id'],
                        'visitable_type' => NewAccountDoctor::class,
                        'created_by' => $user->id,
                        'date' => $parsedDate,
                        // 'user_id' => $user->id,
                    ], [
                        'approval' => null,
                        'reason_id' => $account['reason_id'],
                        'status_active_inactive' => $request->status,
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now(),
                    ]);
                    $model_id = $account['id'];
                    $model_type = Account::class;
                    LogActivity::addLog($model_id, $model_type);
                }
            } else {
                $this->saveWithoutApprovals($request, $parsedDate);
                LogActivity::addLog();
            }
        });
        return $this->respondCreated();
    }
    private function mappingAccountDoctors($accountDoctor)
    {
        return [
            'acc_dr_id' => $accountDoctor->id,
            'acc_line_id' => $accountDoctor->account_lines_id,
            'account_id' => $accountDoctor->account?->id,
            'account' => $accountDoctor->account?->name,
            'type' => $accountDoctor->account?->type->name,
            'line' => $accountDoctor->accountLine?->line->name,
            'division' => $accountDoctor->accountLine?->line_division->name,
            'doctor_id' => $accountDoctor->doctor?->id,
            'doctor' => $accountDoctor->doctor?->name,
            'speciality' => $accountDoctor->doctor?->speciality->name,
            'date' => $accountDoctor->details?->date,
            'employee' => $accountDoctor->details?->by?->fullname,
            'status' => $accountDoctor->details?->status_active_inactive,
            'reason_id' => null,
            'reason' => Reason::where('request_type', NewAccountDoctor::class)
                ->where('id', $accountDoctor->details?->reason_id)->first()?->reason ?? '',
            'visitable_type' => NewAccountDoctor::class
        ];
    }
    private function mappingAccounts($accountLine)
    {
        return [
            'acc_line_id' => $accountLine->id,
            'account_id' => $accountLine->accounts?->id,
            'account' => $accountLine->accounts?->name,
            'type' => $accountLine->accounts?->type->name,
            'line' => $accountLine->line->name,
            'division' => $accountLine->line_division->name,
            'date' => $accountLine->details?->date,
            'employee' => $accountLine->details?->by?->fullname,
            'status' => $accountLine->details?->status_active_inactive,
            'reason_id' => null,
            'reason' => Reason::where('request_type', NewAccountDoctor::class)
                ->where('id', $accountLine->details?->reason_id)->first()?->reason ?? '',
            'visitable_type' => AccountLines::class
        ];
    }
    public function getAccounts(Request $request)
    {
        $from_date = Carbon::parse($request->from_date)->startOfDay();
        $to_date = Carbon::parse($request->to_date)->endOfDay();
        $reasons = Reason::where('request_type', NewAccountDoctor::class)->get();
        $settingPerAccount = ListType::first()->favourite_type == 'Account' ? true : false;
        $accounts = collect([]);
        $activeAccounts = collect([]);
        $inactiveAccounts = collect([]);
        $fields = collect([]);
        if ($settingPerAccount) {
            $accounts = AccountLines::whereHas('details', fn($q) => $q->where('visitable_type', AccountLines::class)
                ->whereBetween('created_at', [$from_date, $to_date])
                ->whereIntegerInRaw('created_by', $request->users_id)
                ->whereNull('approval'))
                ->with('details', 'accounts.type')
                ->get();
            foreach ($accounts as $account) {
                $data = $this->mappingAccounts($account);
                if ($account->details->status_active_inactive == 2) {
                    $inactiveAccounts[] = $data;
                } else {
                    $activeAccounts[] = $data;
                }
            }

            $fields = collect(["s", "account_id", "line", "division", "employee", "account", "type", "approvals", "acc_line_id", "date", "reason"]);
        } else {
            $accounts = NewAccountDoctor::whereHas('details', fn($q) => $q->where('visitable_type', NewAccountDoctor::class)
                ->whereBetween('created_at', [$from_date, $to_date])
                ->whereIntegerInRaw('created_by', $request->users_id)
                ->whereNull('approval'))
                ->with('details', 'account.type', 'doctor.speciality', 'accountLine')
                ->get();
            foreach ($accounts as $account) {
                $data = $this->mappingAccountDoctors($account);
                if ($account->details->status_active_inactive == 2) {
                    $inactiveAccounts[] = $data;
                } else {
                    $activeAccounts[] = $data;
                }
            }

            $fields = collect(["s", "account_id", "line", "division", "employee", "account", "type", "doctor", "speciality", "approvals", "acc_dr_id", "date", "reason"]);
        }

        return $this->respond([
            'inactive' => $inactiveAccounts,
            'active' => $activeAccounts,
            'fields' => $fields,
            'reasons' => $reasons,
            'settingPerAccount' => $settingPerAccount,
        ]);
    }

    public function approvePerDoctor($accounts, $users, $authUser, $now)
    {
        DB::transaction(function () use ($accounts, $users, $authUser, $now) {
            foreach ($accounts as $account) {
                if ($account['reason_id'] == null) {
                    $doctor = NewAccountDoctor::where('account_lines_id', $account['acc_line_id'])
                        ->where('account_id', $account['account_id'])
                        ->where('doctor_id', $account['doctor_id'])
                        ->latest()->first();
                    $detail = PlanVisitDetails::where('visitable_type', NewAccountDoctor::class)
                        ->where('visitable_id', $account['visitable_id'])->first();
                    $users->push($detail->by);
                    $detail->update([
                        'user_id' => $authUser->id,
                        'approval' => 1
                    ]);
                    if ($account['status'] == 1) {
                        if ($doctor->from_date > $now) {
                            $doctor->update([
                                'from_date' => $detail->date,
                                'to_date' => Null
                            ]);
                        }
                        if ($doctor->to_date && $doctor->to_date <= $now) {
                            NewAccountDoctor::create([
                                'account_id' => $doctor->account_id,
                                'line_id' => $doctor->line_id,
                                'doctor_id' => $doctor->doctor_id,
                                'account_lines_id' => $doctor->account_lines_id,
                                'from_date' => $detail->date,
                                'to_date' => null,
                            ]);
                        }
                    } else {
                        if (isNullable($doctor->to_date) && Carbon::parse($doctor->from_date)->toDateString() < $detail->date) {
                            $doctor->update([
                                'to_date' => $detail->date,
                            ]);
                        }
                    }
                    $model_id = $doctor->id;
                    $model_type = NewAccountDoctor::class;
                    LogActivity::addLog($model_id, $model_type);
                    $detail->delete();
                }
            }
        });
        NotificationHelper::send(
            $users->unique('id'),
            new ActiveInactiveApprovedNotification('Active & Inactive Accounts Get Approved', auth()->user())
        );
    }

    public function approvePerAccount($accounts, $users, $authUser, $now)
    {
        DB::transaction(function () use ($accounts, $users, $authUser, $now) {
            foreach ($accounts as $account) {
                if ($account['reason_id'] == null) {
                    $accountLine = AccountLines::find($account['acc_line_id']);
                    $detail = PlanVisitDetails::where('visitable_type', AccountLines::class)
                        ->where('visitable_id', $account['visitable_id'])
                        ->first();
                    $users->push($detail->by);
                    $detail->update([
                        'user_id' => $authUser->id,
                        'approval' => 1
                    ]);
                    if ($account['status'] == 1) {
                        if ($accountLine->from_date > $now) {
                            $accountLine->update([
                                'from_date' => $detail->date,
                                'to_date' => Null
                            ]);
                            NewAccountDoctor::where('account_id', $accountLine->account_id)
                                ->where('account_lines_id', $accountLine->id)->where('line_id', $accountLine->line_id)
                                ->update([
                                    'from_date' => $detail->date,
                                    'to_date' => null,
                                ]);
                        }
                        if ($accountLine->from_date < $now && $accountLine->to_date && $accountLine->to_date <= $now) {
                            $newAccountLine = AccountLines::create([
                                'account_id' => $accountLine->account_id,
                                'line_id' => $accountLine->line_id,
                                'line_division_id' => $accountLine->line_division_id,
                                'brick_id' => $accountLine->brick_id,
                                'class_id' => $accountLine->class_id,
                                'visit_id' => $accountLine->visit_id,
                                'll' => $accountLine->ll,
                                'lg' => $accountLine->lg,
                                'from_date' => $detail->date,
                                'to_date' => null,
                            ]);
                            $newAccountDoctors = NewAccountDoctor::where('account_id', $accountLine->account_id)
                                ->where('account_lines_id', $accountLine->id)->where('line_id', $accountLine->line_id)->get();
                            foreach ($newAccountDoctors as $newAccountDoctor) {
                                NewAccountDoctor::create([
                                    'account_id' => $accountLine->account_id,
                                    'line_id' => $accountLine->line_id,
                                    'doctor_id' => $newAccountDoctor->doctor_id,
                                    'account_lines_id' => $newAccountLine->id,
                                    'from_date' => $detail->date,
                                    'to_date' => null,
                                ]);
                            }
                        }
                    } else {
                        if (isNullable($accountLine->to_date) && Carbon::parse($accountLine->from_date)->toDateString() < $detail->date) {
                            $accountLine->update([
                                'to_date' => $detail->date,
                            ]);
                            NewAccountDoctor::where('account_id', $accountLine->account_id)
                                ->where('account_lines_id', $accountLine->id)->where('line_id', $accountLine->line_id)->update([
                                    'to_date' => $detail->date,
                                ]);
                        }
                    }
                    $model_id = $accountLine->id;
                    $model_type = AccountLines::class;
                    LogActivity::addLog($model_id, $model_type);
                    $detail->delete();
                }
            }
        });
        NotificationHelper::send(
            $users->unique('id'),
            new ActiveInactiveApprovedNotification('Active & Inactive Accounts Get Approved', auth()->user())
        );
    }
    public function accept(Request $request)
    {
        $accounts = $request->accounts;
        /**@var User $authUser */
        $authUser = Auth::user();
        $users = collect([]);
        $now = Carbon::now();
        $settingPerAccount = ListType::first()->favourite_type == 'Account' ? true : false;
        if ($settingPerAccount) {
            $this->approvePerAccount($accounts, $users, $authUser, $now);
        } else {
            $this->approvePerDoctor($accounts, $users, $authUser, $now);
        }


        return $this->respondSuccess();
    }

    public function reject(Request $request)
    {
        $accounts = $request->accounts;
        /**@var User $authUser */
        $authUser = Auth::user();
        $users = collect([]);
        foreach ($accounts as $account) {
            $data = resolve($account['visitable_type'])->find($account['visitable_id']);
            $details = $data->details;
            $users->push($details->by);

            $details->user_id = Auth::id();
            $details->approval = 0;
            $details->save();
            $model_id = $account['visitable_id'];
            $model_type = $account['visitable_type'];
            LogActivity::addLog($model_id, $model_type);
            $details->delete();
        }
        NotificationHelper::send(
            $users->unique('id'),
            new ActiveInactiveApprovedNotification('Active & Inactive Accounts Get Disapproved', auth()->user())
        );
        return $this->respondSuccess();
    }


    // public function lines()
    // {
    //     /**@var User $user */
    //     $user = Auth::user();
    //     $setting = ListType::first()->favourite_type == 'Doctor' ? true : false;
    //     $today = Carbon::now()->startOfMonth();
    //     $months = [$today->toDateString(), Carbon::parse($today)->addMonth(1)->startOfMonth()->toDateString()];
    //     return $this->respond(['lines' => $user->userLines(), 'setting' => $setting, 'months' => $months, 'current_month' => $today->format('M-Y')]);
    // }
    public function getDate(Request $request)
    {
        $months = [];
        $startOfMonth = Carbon::now()->startOfMonth();
        $endOfMonth = Carbon::now()->endOfMonth();
        if ($request->status == 2) {
            $months = [Carbon::now()->addMonth(-1)->endOfMonth()->format('M-Y'), $endOfMonth->format('M-Y')];
        } else {
            $months = [$startOfMonth->format('M-Y'), Carbon::parse($startOfMonth)->addMonth(1)->startOfMonth()->format('M-Y')];
        }
        return $this->respond($months);
    }
    public function lineDivisions(Line $line)
    {
        $division_type = DivisionType::where('last_level', '=', 1)->get('id');
        /**@var User $user */
        $user = Auth::user();
        $divisions = $user->userDivisions($line)->where('division_type_id', $division_type->first()->id);
        $specialities = $line->specialities;
        $classes = $line->classes;
        $accountTypes = AccountType::select('id', 'name')->get();
        return response()->json([
            'divisions' => $divisions->values(),
            'specialities' => $specialities,
            'classes' => $classes,
            'accountTypes' => $accountTypes,
        ]);
    }
    public function divisionBricks(Request $request)
    {
        // $bricks = LineBricks::whereIn('line_division_id', $request->divisions)
        //     ->where(fn($q) => $q->where('to_date', '=', null)->orWhere('to_date', '>=', (string)Carbon::now()))
        //     ->with('brick')->get()->pluck('brick');
        $bricks = Brick::whereHas('lineBricks',fn($q) => $q->whereIn('line_division_id', $request->divisions))->get();
        
        return $this->respond($bricks);
    }
    public function filterOfEmployees(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $users = collect([]);
        $types = [DivisionType::class, Position::class];
        foreach ($types as $type) {
            $users = $users->merge($user->planableUsers($request->line_id, NewAccountDoctor::class, $type));
        }
        return response()->json(['filtered_users' => $users->unique('id')->values()]);
    }
}
