<?php

namespace App\Http\Controllers;

use App\AccountType;
use App\ActualVisit;
use App\ActualVisitSetting;
use App\DivisionType;
use App\Exceptions\CrmException;
use App\Line;
use App\Models\OffDay;
use App\OwActualVisit;
use App\PublicHoliday;
use App\Services\VacationTrackingService;
use App\Services\OwTrackingService;
use App\Shift;
use App\Vacation;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class EmployeeTrackingReportController extends ApiController
{
    private VacationTrackingService $vacationService;
    private OwTrackingService $owService;

    public function __construct(VacationTrackingService $vacationService, OwTrackingService $owService)
    {
        $this->vacationService = $vacationService;
        $this->owService = $owService;
    }

    public function filter(Request $request)
    {
        /**@var User authUser */
        $authUser = Auth::user();
        $tracking = $request->trackingFilter;
        $from = Carbon::parse($tracking['fromDate'])->startOfDay();
        $to = Carbon::parse($tracking['toDate'])->endOfDay();
        $year = Carbon::parse($tracking['fromDate'])->format('Y');
        $showOnlyNoShowData = $tracking['includeNoShowData'] ?? false;

        $dates = collect([]);
        $datesFields = collect([]);
        $period = CarbonPeriod::create($from, $to);

        // If filtering for no-show data only, we'll determine the dates after processing
        if (!$showOnlyNoShowData) {
            // Normal behavior: include all dates
            foreach ($period as $date) {
                $dates->put($date->format('m/d'), "");
                $datesFields->push($date->format('m/d'));
            }
        }
        $shifts = Shift::when(!empty($tracking['shifts']), fn($q) => $q->whereIntegerInRaw("shifts.id", $tracking['shifts']))->get();
        $lines = Line::when(!empty($tracking['lines']), fn($q) => $q->whereIntegerInRaw("lines.id", $tracking['lines']))->get();
        $lineIds = $lines->pluck('id');

        $filtered = new Collection([]);
        $data = new Collection([]);
        $settingVisitsPerShift = ActualVisitSetting::where('key', 'visits_employee_tracking_with_shift')->value('value') == 'Yes';
        $condition = $settingVisitsPerShift ?  'shift_id' : 'acc_type_id';

        foreach ($lines as $line) {
            $users = $line->users($from, $to)
                ->when(!empty($tracking['users']), fn($q) => $q->whereIntegerInRaw("line_users.user_id", $tracking['users']))->get();
            $filtered = $filtered->merge($authUser->filterUsers($line, $users, $tracking));
        }

        // If filtering for no-show data only, collect no-show dates first
        if ($showOnlyNoShowData) {
            $noShowDates = collect([]);

            // Process each user to find dates with no-show data
            $filtered->unique('id')->values()->each(function ($user) use ($settingVisitsPerShift, $condition, $lineIds, $from, $to, $shifts, $year, $period, &$noShowDates) {
                foreach ($shifts as $shift) {
                    $visitValues = $settingVisitsPerShift ? [$shift->id] : AccountType::where('shift_id', $shift->id)->get()->pluck('id');
                    foreach ($period as $date) {
                        $dateKey = $date->format('m/d');
                        $dateString = Carbon::parse($date)->format($year . '-m-d');

                        // Check if this date has no-show data for this user/shift
                        $hasVacation = $this->vacationService->hasVacations($user, $dateString, $shift->id);
                        $hasOw = $this->owService->hasOw($user, $dateString, $shift->id);
                        $hasMixedVacationOw = $this->checkMixedVacationOw($user, $dateString, $shift->id);
                        if (
                            !$user->hasVisits($dateString, $condition, $visitValues) &&
                            !$hasVacation &&
                            !$hasOw &&
                            !$hasMixedVacationOw &&
                            !OffDay::isOffDay($from, $to, $dateString, $shift->id, $lineIds[0]) &&
                            !PublicHoliday::isPublicHoliday($from, $to, $dateString, $lineIds[0])
                        ) {
                            $noShowDates->put($dateKey, "");
                        }
                    }
                }
            });

            // Set dates and datesFields to only include no-show dates
            $dates = $noShowDates;
            $datesFields = $noShowDates->keys();
        }

        $fields = collect(['line', 'employee', 'code', 'position', 'shift'])->merge($datesFields);
        // Process data with the determined dates
        $filtered->unique('id')->values()->each(function ($object) use ($lineIds, $data, $tracking, $from, $to, $dates, $shifts, $year, $settingVisitsPerShift, $condition) {
            $data = $data->push($this->statistics($lineIds, $object,  $from, $to, $tracking, $dates, $shifts, $year, $settingVisitsPerShift, $condition));
        });

        return response()->json([
            'data' => collect($data)->collapse(),
            'datesFields' => $datesFields,
            'fields' => $fields,
        ]);
    }

    private function statistics($lineIds, $user, $from, $to, $tracking, Collection $dates, $shifts, $year, $settingVisitsPerShift, $condition)
    {
        // Check if only no-show data should be shown
        $showOnlyNoShowData = $tracking['includeNoShowData'] ?? false;

        $shifts = $shifts->map(function ($shift) use ($lineIds, $user, $dates, $from, $to, $year, $showOnlyNoShowData, $settingVisitsPerShift, $condition) {
            $visitValues = $settingVisitsPerShift ? [$shift->id] : AccountType::where('shift_id', $shift->id)->get()->pluck('id');
            $dates = collect($dates)->map(function ($value, $key) use ($condition, $year, $user, $shift, $visitValues, $from, $to, $lineIds, $showOnlyNoShowData) {
                $key = Carbon::parse($key)->format($year . '-m-d');

                // If filtering for no-show only, only return no-show data
                if ($showOnlyNoShowData) {
                    $hasVacation = $this->vacationService->hasVacations($user, $key, $shift->id);
                    $hasOw = $this->owService->hasOw($user, $key, $shift->id);
                    $hasMixedVacationOw = $this->checkMixedVacationOw($user, $key, $shift->id);
                    if (
                        !$user->hasVisits($key, $condition, $visitValues) &&
                        !$hasVacation &&
                        !$hasOw &&
                        !$hasMixedVacationOw &&
                        !OffDay::isOffDay($from, $to, $key, $shift->id, $lineIds[0]) &&
                        !PublicHoliday::isPublicHoliday($from, $to, $key, $lineIds[0])
                    ) {
                        return [
                            'data' => 'No',
                            'valueColor' => 'red'
                        ];
                    }
                    // Return empty for non-no-show dates when filtering
                    return "";
                }

                // Normal processing when not filtering for no-show only
                if ($user->hasVisits($key, $condition, $visitValues)) {
                    $actual = ActualVisit::where('user_id', $user->id)
                        ->whereDate('visit_date', $key)
                        ->whereIn($condition, $visitValues)->count() ?? 0;
                    if ($actual !== 0) {
                        return [
                            'data' => $actual,
                            'valueColor' => 'blue'
                        ];
                    }
                    return;
                }
                // Enhanced OW check that handles AM/PM and full-day OW
                $owStatus = $this->owService->getOwSummary($user, $key);
                if ($owStatus['status'] === 'ow') {
                    // Check if this shift is affected by the OW
                    if (in_array($shift->id, $owStatus['affected_shifts'])) {
                        return [
                            'data' => $owStatus['display'],
                            'valueColor' => $owStatus['color']
                        ];
                    }
                }

                // Fallback to original OW check for backward compatibility
                // if ($user->hasOw($key, $shift->id)) {
                //     $ow = OwActualVisit::where('user_id', $user->id)
                //         ->whereDate('date', $key)
                //         ->where(fn($q) => $q->where('shift_id', $shift->id)->orWhere('shift_id', null))->count() ?? 0;
                //     if ($ow !== 0) {
                //         return [
                //             'data' => 'ow',
                //             'valueColor' => 'green'
                //         ];
                //     }
                //     return;
                // }
                if (OffDay::isOffDay($from, $to, $key, $shift->id, $lineIds[0])) {
                    return [
                        'data' => 'off',
                        'valueColor' => '#f39c12'
                    ];
                }
                if (PublicHoliday::isPublicHoliday($from, $to, $key, $lineIds[0])) {
                    return [
                        'data' => 'H',
                        'valueColor' => '#FF00FF'
                    ];
                }

                // Enhanced vacation check that handles AM/PM and full-day vacations
                $vacationStatus = $this->vacationService->getVacationSummary($user, $key);
                $owStatus = $this->owService->getOwSummary($user, $key);

                // Check for mixed vacation/OW scenario (AM vacation + PM OW or vice versa)
                $hasMixedVacationOw = $this->checkMixedVacationOw($user, $key, $shift->id);
                if ($hasMixedVacationOw) {
                    return [
                        'data' => 'v',
                        'valueColor' => 'brown'
                    ];
                }

                if ($vacationStatus['status'] === 'vacation') {
                    // Check if this shift is affected by the vacation
                    if (in_array($shift->id, $vacationStatus['affected_shifts'])) {
                        return [
                            'data' => $vacationStatus['display'],
                            'valueColor' => $vacationStatus['color']
                        ];
                    }
                }
                $hasVacation = $this->vacationService->hasVacations($user, $key, $shift->id);
                $hasOw = $this->owService->hasOw($user, $key, $shift->id);
                $hasMixedVacationOw = $this->checkMixedVacationOw($user, $key, $shift->id);
                if (
                    !$user->hasVisits($key, $condition, $visitValues) &&
                    !$hasVacation &&
                    !$hasOw &&
                    !$hasMixedVacationOw &&
                    !OffDay::isOffDay($from, $to, $key, $shift->id, $lineIds[0]) &&
                    !PublicHoliday::isPublicHoliday($from, $to, $key, $lineIds[0])
                ) {
                    return [
                        'data' => 'No',
                        'valueColor' => 'red'
                    ];
                }
                return "";
            });
            return [
                'id' => $user->id,
                'line' => $user->lines()->whereIntegerInRaw('line_users.line_id', $lineIds)->pluck('name')->implode(','),
                'division' => $user->divisions()->whereIntegerInRaw('line_divisions.line_id', $lineIds)->pluck('name')->implode(','),
                'position' => $user->menuroles,
                'employee' => $user->fullname,
                'code' => $user->emp_code ?? '',
                'shift' => $shift->name,
                'shift_id' => $shift->id,
                'color' =>  $user->hasPosition() ? $user->positionDivision()->first()?->DivisionType->color : $user->divisions()?->first()?->DivisionType->color,
                ...$dates
            ];
        });
        return $shifts;
    }

    public function excel(Request $request)
    {
        /**@var User authUser */
        $authUser = Auth::user();
        $tracking = $request->trackingFilter;
        $from = Carbon::parse($tracking['fromDate'])->startOfDay();
        $to = Carbon::parse($tracking['toDate'])->endOfDay();
        $year = Carbon::parse($tracking['fromDate'])->format('Y');
        $showOnlyNoShowData = $tracking['includeNoShowData'] ?? false;

        $period = CarbonPeriod::create($from, $to);
        $periodMonths = CarbonPeriod::create($from, '1 month', $to);

        // Get the months and format them as you need
        $months = [];
        foreach ($periodMonths as $date) {
            $months[] = $date->format('F'); // 'F' gives full month name, use 'm' for month number
        }
        $dates[] = [
            'Month' => implode(', ', $months),
            'Year' => $year,
        ];

        $shifts = Shift::when(!empty($tracking['shifts']), fn($q) => $q->whereIntegerInRaw("shifts.id", $tracking['shifts']))->get();
        $lines = Line::when(!empty($tracking['lines']), fn($q) => $q->whereIntegerInRaw("lines.id", $tracking['lines']))->get();
        $lineIds = $lines->pluck('id');

        $filtered = new Collection([]);
        $data = new Collection([]);

        foreach ($lines as $line) {
            $users = $line->users($from, $to)
                ->when(!empty($tracking['users']), fn($q) => $q->whereIntegerInRaw("line_users.user_id", $tracking['users']))->get();
            $filtered = $filtered->merge($authUser->filterUsers($line, $users, $tracking));
        }

        // If filtering for no-show data only, filter the period to only include no-show dates
        if ($showOnlyNoShowData) {
            $noShowDates = collect([]);

            // Process each user to find dates with no-show data
            $settingVisitsPerShift = ActualVisitSetting::where('key', 'visits_employee_tracking_with_shift')->value('value') == 'Yes';
            $condition = $settingVisitsPerShift ?  'shift_id' : 'acc_type_id';
            $filtered->unique('id')->values()->each(function ($user) use ($settingVisitsPerShift, $condition, $lineIds, $from, $to, $shifts, $year, $period, &$noShowDates) {
                foreach ($shifts as $shift) {
                    $visitValues = $settingVisitsPerShift ? [$shift->id] : AccountType::where('shift_id', $shift->id)->get()->pluck('id');
                    foreach ($period as $date) {
                        $dateString = Carbon::parse($date)->format($year . '-m-d');

                        // Check if this date has no-show data for this user/shift
                        $hasVacation = $this->vacationService->hasVacations($user, $dateString, $shift->id);
                        $hasOw = $this->owService->hasOw($user, $dateString, $shift->id);
                        $hasMixedVacationOw = $this->checkMixedVacationOw($user, $dateString, $shift->id);
                        if (
                            !$user->hasVisits($dateString, $condition, $visitValues) &&
                            !$hasVacation &&
                            !$hasOw &&
                            !$hasMixedVacationOw &&
                            !OffDay::isOffDay($from, $to, $dateString, $shift->id, $lineIds[0]) &&
                            !PublicHoliday::isPublicHoliday($from, $to, $dateString, $lineIds[0])
                        ) {
                            $noShowDates->push($date);
                        }
                    }
                }
            });

            // Update period to only include no-show dates
            $period = $noShowDates->unique();
        }
        $filtered->unique('id')->values()->each(function ($object) use ($settingVisitsPerShift, $condition, $period, $lineIds, $data, $from, $to, $shifts, $year, $tracking) {
            $data = $data->push($this->statisticEexcel($period, $lineIds, $object, $from, $to, $shifts, $year, $tracking, $condition, $settingVisitsPerShift));
        });

        return response()->json([
            'data' => $data->collapse(),
            'dates' => $dates,
        ]);
    }

    private function statisticEexcel($period, $lineIds, $user, $from, $to, $shifts, $year, $tracking, $condition, $settingVisitsPerShift)
    {
        // Check if only no-show data should be shown
        $showOnlyNoShowData = $tracking['includeNoShowData'] ?? false;

        $lines = $user->lines()->whereIntegerInRaw('line_users.line_id', $lineIds)->pluck('name');
        $divisions = $user->divisions()->whereIntegerInRaw('line_divisions.line_id', $lineIds)->pluck('name');
        $color = $user->hasPosition() ? $user->positionDivision()->first()?->DivisionType->color : $user->divisions()?->first()?->DivisionType->color;
        $data = collect([]);

        foreach ($shifts as $shift) {
            $row = collect([
                'id' => $user->id,
                'line' => $lines->implode(','),
                'division' => $divisions->implode(','),
                'position' => $user->menuroles,
                'employee' => $user->fullname,
                'code' => $user->emp_code,
                'shift' => $shift->name,
                'shift_id' => $shift->id,
                'color' =>  $color
            ]);
            $visitValues = $settingVisitsPerShift ? [$shift->id] : AccountType::where('shift_id', $shift->id)->get()->pluck('id');
            foreach ($period as $date) {
                $formated = Carbon::parse($date)->format('m/d');

                // If filtering for no-show only, only process no-show cases
                if ($showOnlyNoShowData) {
                    $hasVacation = $this->vacationService->hasVacations($user, $date, $shift->id);
                    $hasOw = $this->owService->hasOw($user, $date, $shift->id);
                    $hasMixedVacationOw = $this->checkMixedVacationOw($user, $date, $shift->id);
                    if (
                        !$user->hasVisits($date, $condition, $visitValues) &&
                        !$hasVacation &&
                        !$hasOw &&
                        !$hasMixedVacationOw &&
                        !OffDay::isOffDay($from, $to, $date, $shift->id, $lineIds[0]) &&
                        !PublicHoliday::isPublicHoliday($from, $to, $date, $lineIds[0])
                    ) {
                        $row = $row->put($formated, 'No Show');
                    }
                    continue;
                }

                // Normal processing when not filtering for no-show only
                if ($user->hasVisits($date, $condition, $visitValues)) {
                    $actual = ActualVisit::where('user_id', $user->id)
                        ->whereDate('visit_date', Carbon::parse($date)->toDateString())
                        ->whereIn($condition, $visitValues)->count() ?? 0;
                    if ($actual !== 0) {
                        $row = $row->put($formated, '(visits) ' . $actual);
                    }
                    continue;
                }
                // Enhanced OW check for Excel export
                $owStatus = $this->owService->getOwSummary($user, $date);
                if ($owStatus['status'] === 'ow') {
                    // Check if this shift is affected by the OW
                    if (in_array($shift->id, $owStatus['affected_shifts'])) {
                        $row = $row->put($formated, 'ow');
                        continue;
                    }
                }

                // Fallback to original OW check for backward compatibility
                // if ($user->hasOw($date, $shift->id)) {
                //     $ow = OwActualVisit::where('user_id', $user->id)
                //         ->whereDate('date', Carbon::parse($date)->toDateString())
                //         ->where(fn($q) => $q->where('shift_id', $shift->id)->orWhere('shift_id', null))->count() ?? 0;
                //     if ($ow !== 0) {
                //         $row = $row->put($formated, '(ow) ' . $ow);
                //     }
                //     continue;
                // }
                if (OffDay::isOffDay($from, $to, $date, $shift->id, $lineIds[0])) {
                    $row = $row->put($formated, 'Off Day');
                    continue;
                }
                if (PublicHoliday::isPublicHoliday($from, $to, $date, $lineIds[0])) {
                    $row = $row->put($formated, 'Public Holiday');
                    continue;
                }

                // Check for mixed vacation/OW scenario (AM vacation + PM OW or vice versa)
                $hasMixedVacationOw = $this->checkMixedVacationOw($user, $date, $shift->id);
                if ($hasMixedVacationOw) {
                    $row = $row->put($formated, 'vacation');
                    continue;
                }

                // Enhanced vacation check for Excel export
                $vacationStatus = $this->vacationService->getVacationSummary($user, $date);
                if ($vacationStatus['status'] === 'vacation') {
                    // Check if this shift is affected by the vacation
                    if (in_array($shift->id, $vacationStatus['affected_shifts'])) {
                        $row = $row->put($formated, 'vacation');
                    }
                    continue;
                }
                $hasVacation = $this->vacationService->hasVacations($user, $date, $shift->id);
                $hasOw = $this->owService->hasOw($user, $date, $shift->id);
                $hasMixedVacationOw = $this->checkMixedVacationOw($user, $date, $shift->id);
                if (
                    !$user->hasVisits($date, $condition, $visitValues) &&
                    !$hasVacation &&
                    !$hasOw &&
                    !$hasMixedVacationOw &&
                    !OffDay::isOffDay($from, $to, $date, $shift->id, $lineIds[0]) &&
                    !PublicHoliday::isPublicHoliday($from, $to, $date, $lineIds[0])
                ) {
                    $row = $row->put($formated, 'No Show');
                    continue;
                }
            }
            $data = $data->push($row);
        }
        return $data;
    }

    /**
     * Check if user has mixed vacation/OW scenario (AM vacation + PM OW or vice versa)
     * If so, treat all shifts as vacation
     *
     * @param \App\User $user
     * @param string $date
     * @param int $shiftId
     * @return bool
     */
    private function checkMixedVacationOw(\App\User $user, string $date, int $shiftId): bool
    {
        // Get vacation and OW status for the date
        $vacationStatus = $this->vacationService->getUserVacationStatus($user, $date);
        $owStatus = $this->owService->getUserOwStatus($user, $date);

        // If neither vacation nor OW exists, return false
        if (!$vacationStatus['hasVacation'] && !$owStatus['hasOw']) {
            return false;
        }

        // If either is full day, handle separately (not mixed scenario)
        if ($vacationStatus['vacationType'] === 'full_day' || $owStatus['owType'] === 'full_day') {
            return false;
        }

        // Check for mixed scenarios:
        // 1. AM vacation + PM OW
        // 2. AM OW + PM vacation
        $vacationShifts = $vacationStatus['affectedShifts'] ?? [];
        $owShifts = $owStatus['affectedShifts'] ?? [];

        // Check if we have AM vacation (shift 1) and PM OW (shift 2)
        $hasAmVacationPmOw = in_array(1, $vacationShifts) && in_array(2, $owShifts);

        // Check if we have AM OW (shift 1) and PM vacation (shift 2)
        $hasAmOwPmVacation = in_array(1, $owShifts) && in_array(2, $vacationShifts);

        // If either mixed scenario exists, treat all shifts as vacation
        return $hasAmVacationPmOw || $hasAmOwPmVacation;
    }
}
