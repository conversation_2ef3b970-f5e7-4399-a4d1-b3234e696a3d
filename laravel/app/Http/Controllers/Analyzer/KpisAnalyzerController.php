<?php

namespace App\Http\Controllers\Analyzer;

use App\ActualVisit;
use App\CallRate;
use App\Classes;
use App\ClassFrequency;
use App\DivisionType;
use App\DoctorFrequency;
use App\Exceptions\CrmException;
use App\Http\Controllers\Controller;
use App\Line;
use App\LineDivision;
use App\Models\OffDay;
use App\Models\OtherSetting;
use App\OwActualVisit;
use App\Services\AccountService;
use App\Services\ActualService;
use App\Services\Analyzers\AnalyzerCallRateService;
use App\Services\Analyzers\AnalyzerClassicFrequencyService;
use App\Services\Analyzers\AnalyzerCoverageService;
use App\Services\Analyzers\AnalyzerFrequencyService;
use App\Services\DoctorService;
use App\Services\Enums\AnalyzerCallRate;
use App\Services\Enums\AnalyzerCoverage;
use App\Services\Enums\AnalyzerFrequency;
use App\SpecialityFrequency;
use App\User;
use App\Vacation;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * KPIs Analyzer Controller - Enhanced for Readability
 *
 * This controller processes KPI analysis in a clear, sequential manner:
 *
 * MAIN FLOW:
 * 1. Initialize Analysis Context (filters, dates, settings)
 * 2. Load Base Data (lines, divisions, users)
 * 3. Process Analysis (employee-based OR division-based)
 * 4. Calculate Metrics (coverage, call rate, frequency)
 * 5. Return Results
 *
 * ANALYSIS TYPES:
 * - Employee-Based: Analyzes KPIs grouped by individual employees
 * - Division-Based: Analyzes KPIs grouped by organizational divisions
 *
 * METRICS CALCULATED:
 * - Coverage: How many targets (accounts/doctors) are actually visited
 * - Call Rate: Average visits per working day by shift
 * - Frequency: Comparison of actual vs target visit frequency per doctor
 */
class KpisAnalyzerController extends Controller
{
    private AnalyzerCoverageService $coverageAnalyzer;
    private AnalyzerCallRateService $callRateAnalyzer;
    private AnalyzerFrequencyService $analyzerFrequencyService;
    private AnalyzerClassicFrequencyService $analyzerClassicFrequencyService;

    // employee and division
    private bool $isFilterByEmployee = false;

    // doctor and account
    private bool $isFilterByDoctorType = false;
    private Carbon $from;
    private Carbon $to;
    private int $divisionType;

    // visit filter
    private array $visitFilter;
    private bool $isClassicFrequency;
    private array $months;
    private string $year;
    private User $user;
    private Collection $classes;

    public function __construct()
    {
        $this->classes = Classes::pluck('name');
        $this->coverageAnalyzer = new AnalyzerCoverageService();
        $this->callRateAnalyzer = new AnalyzerCallRateService();
        $this->analyzerFrequencyService = new AnalyzerFrequencyService();
        $this->analyzerClassicFrequencyService = new AnalyzerClassicFrequencyService($this->classes);
        // Don't set user in constructor for Octane compatibility
        // Will be set in each method that needs it
    }

    private function setVisitFilters()
    {
        $this->user = Auth::user();
        $this->isFilterByEmployee = $this->visitFilter['filter'] == 2;
        $this->isFilterByDoctorType = $this->visitFilter['type'] == 2;
    }

    private function checkClassicFrequency()
    {
        $this->isClassicFrequency = ClassFrequency::select([
            'id',
        ])
            ->when(
                !empty($this->visitFilter['lines']),
                fn($q) => $q->whereIntegerInRaw('line_id', $this->visitFilter['lines'])
            )
            ->whereBetween(
                DB::raw("(TRIM(LEADING '0' FROM DATE_FORMAT(crm_class_frequencies.date,'%m')))"),
                $this->months
            )
            ->whereYear('class_frequencies.date', $this->year)->exists();
    }

    private function setDateBoundaries()
    {
        $this->from = Carbon::parse($this->visitFilter['fromDate'])->startOfDay();
        $this->to = Carbon::parse($this->visitFilter['toDate'])->endOfDay();

        $this->months = [(int)$this->from->format('m'), (int)$this->to->format('m')];
        $this->year = (int)$this->from->format('Y');
    }

    private function getDivisionsFilteredByDivisions($lines)
    {

        $filtered = collect();
        foreach ($lines as $line) {
            $divisions = $line->divisions($this->from, $this->to)
                ->when(!empty($this->visitFilter['divisions']), fn($q) => $q->whereIntegerInRaw("line_divisions.id", $this->visitFilter['divisions']))->get();
            $filtered = $filtered->merge($this->user->filterDivisions($line, $divisions, $this->visitFilter, $this->from, $this->to));
        }
        return $filtered->unique('id')->values();
    }

    private function getUsersFilteredByEmployees($lines)
    {
        $filtered = collect();
        foreach ($lines as $line) {
            $users = $line->users($this->from, $this->to)
                ->when(!empty($this->visitFilter['users']), fn($q) => $q->whereIntegerInRaw("line_users.user_id", $this->visitFilter['users']))->get();
            $filtered = $filtered->merge($this->user->filterUsers($line, $users, $this->visitFilter, $this->from, $this->to));
        }
        return $filtered->unique('id')->values();
    }

    /**
     * Main KPI Analysis Filter - Sequential Processing Pipeline
     *
     * This method orchestrates the entire KPI analysis process in a clear sequence:
     * 1. Initialize filters and date boundaries
     * 2. Load base data (lines, divisions)
     * 3. Process data based on filter type (employee vs division)
     * 4. Calculate and return KPI metrics
     */
    public function filter(Request $request)
    {
        // Step 1: Initialize Analysis Context
        $this->initializeAnalysisContext($request);

        // Step 2: Load Base Data
        $lines = $this->loadFilteredLines();

        // Step 3: Process Analysis Based on Filter Type
        $this->processKpiAnalysis($lines);

        // Step 4: Return Calculated Metrics
        return $this->buildKpiResponse();
    }

    /**
     * Step 1: Initialize all analysis context and parameters
     */
    private function initializeAnalysisContext(Request $request): void
    {
        $this->visitFilter = $request->visitFilter;
        $this->setVisitFilters();
        $this->setDateBoundaries();
        $this->checkClassicFrequency();
        $this->divisionType = DivisionType::where('last_level', '=', 1)->value('id');
    }

    /**
     * Step 2: Load filtered lines with their classes
     */
    private function loadFilteredLines(): Collection
    {
        return Line::when(
            !empty($this->visitFilter['lines']),
            fn($q) => $q->whereIntegerInRaw("lines.id", $this->visitFilter['lines'])
        )->with('classes')->get();
    }

    /**
     * Step 3: Process KPI analysis based on filter type
     */
    private function processKpiAnalysis(Collection $lines): void
    {
        $this->isFilterByEmployee
            ? $this->processEmployeeBasedAnalysis($lines)
            : $this->processDivisionBasedAnalysis($lines);
    }

    /**
     * Step 4: Build and return the KPI response
     */
    private function buildKpiResponse(): \Illuminate\Http\JsonResponse
    {
        return response()->json([
            'coverage' => $this->isFilterByDoctorType
                ? $this->coverageAnalyzer->getDoctorCoverage()
                : $this->coverageAnalyzer->getAccountCoverage(),
            'call_rate' => $this->callRateAnalyzer->all(),
            'advancedFrequency' => $this->analyzerFrequencyService->all(),
            'classicFrequency' => !$this->isClassicFrequency ? $this->analyzerFrequencyService->all() :
                $this->analyzerClassicFrequencyService->all(),
        ]);
    }

    /**
     * Process Employee-Based KPI Analysis
     *
     * Sequential steps:
     * 1. Collect employee data and their divisions
     * 2. Extract unique identifiers for analysis
     * 3. Count target accounts/doctors
     * 4. Calculate coverage metrics
     * 5. Calculate call rate metrics
     * 6. Calculate frequency metrics
     */
    private function processEmployeeBasedAnalysis(Collection $lines): void
    {
        // Step 1: Collect Employee Division Data
        $employeeDivisionData = $this->collectEmployeeDivisionData($lines);

        // Step 2: Extract Analysis Identifiers
        $analysisIds = $this->extractEmployeeAnalysisIds($employeeDivisionData, $lines);

        // Step 3: Count Target Entities (Doctors/Accounts)
        $targetEntities = $this->countAccountsAndDoctors($analysisIds['lineIds'], $analysisIds['divIds']);

        // Step 4: Calculate Coverage Metrics
        $this->calculateCoverageMetrics($analysisIds['lineIds'], $targetEntities, $analysisIds['userIds'], 'user_id');

        // Step 5: Calculate Call Rate Metrics
        $this->calculateCallRateMetrics($analysisIds['userIds'], $targetEntities, $analysisIds['lineIds'], 'users.id');

        // Step 6: Calculate Frequency Metrics for Each Employee
        $this->calculateEmployeeFrequencyMetrics($employeeDivisionData, $targetEntities);
        // throw new CrmException($this->calculateEmployeeFrequencyMetrics($employeeDivisionData, $targetEntities));
    }

    /**
     * Step 1: Collect employee division data
     */
    private function collectEmployeeDivisionData(Collection $lines): Collection
    {
        $employeeData = collect([]);
        $this->getUsersFilteredByEmployees($lines)->each(function ($user) use (&$employeeData) {
            [$userLines, $divisionHolders] = $this->getLinesBelowDivisionsBelowUsers($user);
            $employeeData = $employeeData->push($divisionHolders);
        });
        return $employeeData->collapse()->unique('user_id')->values();
    }

    /**
     * Step 2: Extract unique identifiers for analysis
     */
    private function extractEmployeeAnalysisIds(Collection $employeeData, Collection $lines): array
    {
        return [
            'divIds' => $employeeData->pluck('div_id')->unique()->values()->toArray(),
            'userIds' => $employeeData->pluck('user_id')->unique()->values()->toArray(),
            'lineIds' => $lines->pluck('id')->toArray(),
        ];
    }

    /**
     * Step 6: Calculate frequency metrics for each employee
     */
    private function calculateEmployeeFrequencyMetrics(Collection $employeeData, array $targetEntities): void
    {
        $this->calculateFrequencyMetrics(
            $targetEntities['doctors'],
            $employeeData->pluck('user_id')->toArray(),
            'users.id'
        );
    }

    /**
     * Process Division-Based KPI Analysis
     *
     * Sequential steps:
     * 1. Collect division data and their sub-divisions
     * 2. Extract unique identifiers for analysis
     * 3. Count target accounts/doctors
     * 4. Calculate coverage metrics
     * 5. Calculate call rate metrics
     * 6. Calculate frequency metrics
     */
    private function processDivisionBasedAnalysis(Collection $lines): void
    {
        // Step 1: Collect Division Data
        $divisionData = $this->collectDivisionData($lines);

        // Step 2: Extract Analysis Identifiers
        $analysisIds = $this->extractDivisionAnalysisIds($divisionData, $lines);

        // Step 3: Count Target Entities (Doctors/Accounts)
        $targetEntities = $this->countAccountsAndDoctors($analysisIds['lineIds'], $analysisIds['divIds']);

        // Step 4: Calculate Coverage Metrics
        $this->calculateCoverageMetrics($analysisIds['lineIds'], $targetEntities, $analysisIds['divIds'], 'div_id');

        // Step 5: Calculate Call Rate Metrics
        $this->calculateCallRateMetrics($analysisIds['divIds'], $targetEntities, $analysisIds['lineIds'], 'users.id');

        // Step 6: Calculate Frequency Metrics for Each Division
        $this->calculateDivisionFrequencyMetrics($divisionData, $targetEntities);
    }

    /**
     * Step 1: Collect division data
     */
    private function collectDivisionData(Collection $lines): Collection
    {
        $divisionData = collect([]);
        $this->getDivisionsFilteredByDivisions($lines)->each(function ($division) use (&$divisionData) {
            [$divisionLines, $divisionHolders] = $this->getLinesBelowDivisions($division);
            $divisionData = $divisionData->push($divisionHolders);
        });
        return $divisionData->collapse()->unique('id')->values();
    }

    /**
     * Step 2: Extract unique identifiers for analysis
     */
    private function extractDivisionAnalysisIds(Collection $divisionData, Collection $lines): array
    {
        return [
            'divIds' => $divisionData->pluck('id')->unique()->values()->toArray(),
            'lineIds' => $lines->pluck('id')->toArray(),
        ];
    }

    /**
     * Step 6: Calculate frequency metrics for each division
     */
    private function calculateDivisionFrequencyMetrics(Collection $divisionData, array $targetEntities): void
    {
        $this->calculateFrequencyMetrics(
            $targetEntities['doctors'],
            $divisionData->pluck('id')->toArray(),
            'line_divisions.id'
        );
    }

    private function getLinesBelowDivisions(LineDivision $division): array
    {
        $lines = $division->line()->get()->pluck('id')->toArray();
        $divisions = $division->getBelowDivisions(from: $this->from, to: $this->to)
            ->where('division_type_id', $this->divisionType)
            ->unique('div_id');

        return [$lines, $divisions];
    }

    private function getLinesBelowDivisionsBelowUsers(User $user): array
    {
        $lines = $user->lines($this->from, $this->to)->pluck('lines.id')->toArray();
        $divisions = $user->allBelowUsersWithDivision(from: $this->from, to: $this->to)
            ->where('div_type_id', $this->divisionType)
            ->unique('user_id');
        return [$lines, $divisions];
    }


    /**
     * @throws \Exception
     */
    private function countAccountsAndDoctors(array $lines, array $divisions): array
    {

        $doctors = $this->isFilterByDoctorType
            ? (new DoctorService)->getDoctorsWithFrequency(
                lines: $lines,
                divisions: $divisions,
                from: $this->from,
                to: $this->to,
                months: [$this->from->format('m'), $this->to->format('m')],
                year: $this->from->format('Y'),
                specialities: $this->visitFilter['specialities'],
                accountTypes: $this->visitFilter['accountTypes'],
            )
            : collect();
        $accounts = !$this->isFilterByDoctorType
            ? (new AccountService)->getAccountsCoverage(
                lines: $lines,
                divisions: $divisions,
                from: $this->from,
                to: $this->to,
                specialities: $this->visitFilter['specialities'],
                accountTypes: $this->visitFilter['accountTypes']
            )
            : collect();
        $countPhDoctors = $doctors->where("acc_shift_id", 3)->count();
        $countAmDoctors = $doctors->where("acc_shift_id", 1)->count();
        $countPmDoctors = $doctors->where("acc_shift_id", 2)->count();
        $countPhAccounts = $accounts->where("acc_shift_id", 3)->count();
        $countAmAccounts = $accounts->where("acc_shift_id", 1)->count();
        $countPmAccounts = $accounts->where("acc_shift_id", 2)->count();


        $this->coverageAnalyzer->addTo(
            AnalyzerCoverage::PH_ACCOUNT_COVERAGE,
            0,
            $countPhAccounts
        );
        $this->coverageAnalyzer->addTo(
            AnalyzerCoverage::AM_ACCOUNT_COVERAGE,
            0,
            $countAmAccounts
        );
        $this->coverageAnalyzer->addTo(
            AnalyzerCoverage::PM_ACCOUNT_COVERAGE,
            0,
            $countPmAccounts
        );
        $this->coverageAnalyzer->addTo(
            AnalyzerCoverage::PH_DOCTOR_COVERAGE,
            0,
            $countPhDoctors
        );
        $this->coverageAnalyzer->addTo(
            AnalyzerCoverage::AM_DOCTOR_COVERAGE,
            0,
            $countAmDoctors
        );
        $this->coverageAnalyzer->addTo(
            AnalyzerCoverage::PM_DOCTOR_COVERAGE,
            0,
            $countPmDoctors
        );

        return [
            'accounts' => $accounts,
            'doctors' => $doctors,
        ];
    }


    private function getVisitsFilteredByDoctors(
        array      $belowDivisions,
        array      $lines,
        Collection $doctors,
        string     $filterBy
    ) {
        $visits = ActualVisit::whereIntegerInRaw($filterBy, $belowDivisions)
            ->whereIntegerInRaw('account_dr_id', $doctors->pluck('doctor_id'))
            ->whereBetween('visit_date', [$this->from, $this->to])
            ->get()->unique('account_dr_id')->count();
        return $visits;
    }

    private function getVisitsFilteredByAccounts(
        array      $belowDivisions,
        array      $lines,
        Collection $accounts,
        string     $filterBy
    ) {
        $visits = ActualVisit::whereIntegerInRaw('line_id', $lines)
            ->whereIntegerInRaw($filterBy, $belowDivisions)
            ->whereIntegerInRaw('account_id', $accounts->pluck('account_id'))
            ->whereBetween('visit_date', [$this->from, $this->to])
            ->get()->unique('account_id')->count();
        return $visits;
    }

    /**
     * Calculate Coverage Metrics
     *
     * Determines how many accounts/doctors are actually covered by visits
     * compared to the total available targets.
     *
     * @throws \Exception
     */
    private function calculateCoverageMetrics(array $lines, array $targetEntities, array $filterIds, string $filterBy): void
    {
        if ($this->isFilterByDoctorType) {
            // Process Doctor Coverage by Shift
            $phDoctors = $targetEntities['doctors']->where("acc_shift_id", 3);
            $amDoctors = $targetEntities['doctors']->where("acc_shift_id", 1);
            $pmDoctors = $targetEntities['doctors']->where("acc_shift_id", 2);

            $phCoverage = $this->getVisitsFilteredByDoctors($filterIds, $lines, $phDoctors, $filterBy);
            $amCoverage = $this->getVisitsFilteredByDoctors($filterIds, $lines, $amDoctors, $filterBy);
            $pmCoverage = $this->getVisitsFilteredByDoctors($filterIds, $lines, $pmDoctors, $filterBy);

            $this->coverageAnalyzer->addTo(AnalyzerCoverage::PH_DOCTOR_COVERAGE, $phCoverage, 0);
            $this->coverageAnalyzer->addTo(AnalyzerCoverage::AM_DOCTOR_COVERAGE, $amCoverage, 0);
            $this->coverageAnalyzer->addTo(AnalyzerCoverage::PM_DOCTOR_COVERAGE, $pmCoverage, 0);
            return;
        }

        // Process Account Coverage by Shift
        $phAccounts = $targetEntities['accounts']->where("acc_shift_id", 3);
        $amAccounts = $targetEntities['accounts']->where("acc_shift_id", 1);
        $pmAccounts = $targetEntities['accounts']->where("acc_shift_id", 2);

        $phCoverage = $this->getVisitsFilteredByAccounts($filterIds, $lines, $phAccounts, $filterBy);
        $amCoverage = $this->getVisitsFilteredByAccounts($filterIds, $lines, $amAccounts, $filterBy);
        $pmCoverage = $this->getVisitsFilteredByAccounts($filterIds, $lines, $pmAccounts, $filterBy);

        $this->coverageAnalyzer->addTo(AnalyzerCoverage::PH_ACCOUNT_COVERAGE, $phCoverage, 0);
        $this->coverageAnalyzer->addTo(AnalyzerCoverage::AM_ACCOUNT_COVERAGE, $amCoverage, 0);
        $this->coverageAnalyzer->addTo(AnalyzerCoverage::PM_ACCOUNT_COVERAGE, $pmCoverage, 0);
    }

    /**
     * Calculate Call Rate Metrics
     *
     * Determines the average number of visits per working day
     * for different shifts (AM, PM, PH).
     */
    private function calculateCallRateMetrics(array $filterIds, array $targetEntities, array $lines, string $filterBy): void
    {
        // Extract doctor IDs for filtering actual visits
        $doctorIds = $targetEntities['doctors']->pluck('doctor_id')->toArray();

        // Get actual visits data
        $actualVisits = (new ActualService)->teamActuals(
            from: $this->from,
            to: $this->to,
            users: $filterIds,
            table: $filterBy,
            lines: $lines,
            doctorIds: $doctorIds,
        );

        // Process call rate only for doctor-type filtering
        if ($this->isFilterByDoctorType) {
            $this->processCallRateByShift($actualVisits);
        }
    }

    /**
     * Process call rate calculations by shift
     */
    private function processCallRateByShift(Collection $actualVisits): void
    {
        // Separate visits by shift
        $phActuals = $actualVisits->where('acc_shift_id', 3);
        $amActuals = $actualVisits->where('acc_shift_id', 1);
        $pmActuals = $actualVisits->where('acc_shift_id', 2);

        // Count unique working days per shift
        $amActualDays = $amActuals->pluck('date')->unique()->count();
        $pmActualDays = $pmActuals->pluck('date')->unique()->count();
        $phActualDays = $phActuals->pluck('date')->unique()->count();

        // Calculate call rates (visits per day)
        $phCallRate = $phActualDays ? round($phActuals->count() / $phActualDays, 0) : 0;
        $pmCallRate = $pmActualDays ? round($pmActuals->count() / $pmActualDays, 0) : 0;
        $amCallRate = $amActualDays ? round($amActuals->count() / $amActualDays, 0) : 0;

        // Store calculated call rates
        $this->callRateAnalyzer->addTo(AnalyzerCallRate::PH_Actual_CALLRATE, $phCallRate, $phActuals->count());
        $this->callRateAnalyzer->addTo(AnalyzerCallRate::AM_Actual_CALLRATE, $amCallRate, $amActuals->count());
        $this->callRateAnalyzer->addTo(AnalyzerCallRate::PM_Actual_CALLRATE, $pmCallRate, $pmActuals->count());
    }

    /**
     * Calculate Frequency Metrics
     *
     * Compares actual visit frequency against target frequency
     * for each doctor and categorizes them as:
     * - Below Frequency: Actual < Target
     * - Meet Frequency: Actual = Target
     * - Above Frequency: Actual > Target
     */
    private function calculateFrequencyMetrics(Collection $doctors, array $userIdOrDivId, string $column): void
    {
        // Get actual visits for the specified user/division and doctors
        $actualVisits = $this->getActualVisitsForFrequencyAnalysis(
            $userIdOrDivId,
            $column,
            $doctors->pluck('doctor_id')->toArray()
        );
        // Analyze frequency for each doctor
        $doctors->each(function ($doctor) use ($actualVisits) {
            $this->analyzeIndividualDoctorFrequency($doctor, $actualVisits);
        });
    }

    /**
     * Analyze frequency performance for an individual doctor
     */
    private function analyzeIndividualDoctorFrequency($doctor, Collection $actualVisits): void
    {
        Log::info('Analyzing individual doctor frequency', ['doctor_id' => $doctor->id]);
        $targetFrequency = $doctor->frequency;
        $actualFrequency = $actualVisits->where('account_dr_id', $doctor->id)->count();
        Log::info('targetFrequency: ' . $targetFrequency);
        Log::info('actualFrequency: ' . $actualFrequency);

        // Handle zero target frequency
        if ($targetFrequency === 0) {
            $this->analyzerFrequencyService->addTo(AnalyzerFrequency::BELOW_FREQUENCY, 1);
            $this->analyzerClassicFrequencyService->addTo($doctor->class, 0, 1, 0);
            return;
        }

        // Categorize frequency performance
        if ($targetFrequency == $actualFrequency) {
            $this->analyzerFrequencyService->addTo(AnalyzerFrequency::MEET_FREQUENCY, 1);
            $this->analyzerClassicFrequencyService->addTo($doctor->class, 1, 0, 0);
        } elseif ($targetFrequency > $actualFrequency) {
            $this->analyzerFrequencyService->addTo(AnalyzerFrequency::BELOW_FREQUENCY, 1);
            $this->analyzerClassicFrequencyService->addTo($doctor->class, 0, 1, 0);
        } else {
            $this->analyzerFrequencyService->addTo(AnalyzerFrequency::ABOVE_FREQUENCY, 1);
            $this->analyzerClassicFrequencyService->addTo($doctor->class, 0, 0, 1);
        }
    }


    /**
     * Get Actual Visits for Frequency Analysis
     *
     * Retrieves actual visit data filtered by user/division and doctors
     * for frequency comparison analysis.
     */
    private function getActualVisitsForFrequencyAnalysis(array $userIdOrDivId, string $column, array $doctors): Collection
    {

        $data = ActualVisit::select([
            'actual_visits.id as id',
            'actual_visits.visit_date',
            'actual_visits.account_dr_id',
            DB::raw("DATE_FORMAT(crm_actual_visits.created_at,'%Y-%m-%d %H:%s:%i') as date"),
            'specialities.id as speciality_id',
            'bricks.name as brick',
            'lines.name as line',
            'line_divisions.name as division',
            'users.fullname as user',
            'accounts.name as account',
            'accounts.id as account_id',
            'account_types.name as acc_type',
            DB::raw('IFNULL(crm_doctors.name,"") as doctor'),
            DB::raw('IFNULL(crm_doctors.id,"") as doctor_id'),
            DB::raw('IFNULL(group_concat(distinct crm_products.name),"") as product'),
            'specialities.name as speciality',
            'visit_types.name as type',
            'plan_visit_details.approval as status'
        ])
            ->leftJoin('lines', 'actual_visits.line_id', 'lines.id')
            ->leftJoin('line_divisions', 'actual_visits.div_id', 'line_divisions.id')
            ->leftJoin('users', 'actual_visits.user_id', 'users.id')
            ->leftJoin('accounts', 'actual_visits.account_id', 'accounts.id')
            ->leftJoin('bricks', 'actual_visits.brick_id', 'bricks.id')
            ->leftJoin('account_types', 'actual_visits.acc_type_id', 'account_types.id')
            ->leftJoin('doctors', 'actual_visits.account_dr_id', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('visit_types', 'actual_visits.visit_type_id', 'visit_types.id')
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('actual_visits.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', 'App\ActualVisit');
                }
            )
            ->leftJoin('actual_visit_products', 'actual_visits.id', 'actual_visit_products.visit_id')
            ->leftJoin('products', 'actual_visit_products.product_id', 'products.id')
            ->where('actual_visits.deleted_at', '=', null)
            ->whereIntegerInRaw('actual_visits.account_dr_id', $doctors)
            ->where(fn($q) => $q->where('plan_visit_details.approval', 1)->orWhere('plan_visit_details.approval', null))
            ->whereIntegerInRaw($column, $userIdOrDivId)
            ->whereBetween(DB::raw("(DATE_FORMAT(crm_actual_visits.visit_date,'%m'))"), $this->months)
            ->whereYear('visit_date', $this->year);

        return $data->groupBy('id', 'plan_visit_details.approval')->get();
    }
}
