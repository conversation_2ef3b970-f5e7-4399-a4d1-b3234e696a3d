<?php

namespace App\Http\Controllers;

use App\Account;
use App\AccountType;
use App\Brick;
use App\Classes;
use App\DivisionType;
use App\Exceptions\CrmException;
use App\Giveaway;
use App\Line;
use App\LineDivisionUser;
use App\LineProduct;
use App\Models\Coaching\Type;
use App\Models\DoubleVisitType;
use App\Models\Expenses\Types\ExpenseType;
use App\Models\FrequencyType;
use App\Models\ListSetting;
use App\Models\ListType;
use App\Models\OtherSetting;
use App\PlanSetting;
use App\Product;
use App\RequestType;
use App\Role;
use App\SalesTypes;
use App\Shift;
use App\Speciality;
use App\User;
use App\UserPosition;
use App\VisitType;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class LinesDataApiController extends ApiController
{
    public function lines(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $requestedData = $request->data;
        $from = $request->from ? Carbon::parse($request->from)->startOfDay() : Carbon::now();
        $to = $request->to ? Carbon::parse($request->to)->endOfDay() : Carbon::now();
        $data = [];
        if (in_array('lines', $requestedData)) {
            $data['lines'] = $user->userLines(from: $from, to: $to);
        }
        if (in_array('lines', $requestedData) && count($data['lines']) > 0 && in_array('another_types', $requestedData)) {
            $is_last_level = $user->divisionType($data['lines'][0])?->last_level == 1;
            if (!$is_last_level) {
                $data['another_types'] = DoubleVisitType::select('id', 'name')->get();
            }
        }

        if (in_array('users', $requestedData)) {
            $data['users'] = $user->belowUsersOfAllLinesWithPositions(lines: $data['lines'], from: $from, to: $to);
        }
        if (in_array('roles', $requestedData)) {
            $roles = collect([]);
            if ($user->hasRole('admin') || $user->hasRole('sub admin') || $user->hasRole('Gemstone Admin')) {
                $roles = Role::whereNotIn('name', ['admin', 'user', 'guest', 'sub Admin'])->get();
            } else {
                foreach ($data['users'] as $user) {
                    $roles = $roles->merge($user->roles);
                }
            }
            $data['roles'] = $roles->unique('id')->whereNotIn('name', ['admin', 'user', 'guest'])->values();
        }
        if (in_array('accountTypes', $requestedData)) {
            $data['accountTypes'] = AccountType::select('id', 'name')->orderBy('sort', 'ASC')->get();
        }
        if (in_array('classes', $requestedData)) {
            $data['classes'] = Classes::select('id', 'name')->orderBy('sort', 'ASC')->get();
        }
        if (in_array('expenseTypes', $requestedData)) {
            $data['expenseTypes'] = ExpenseType::select('id', 'name')->orderBy('sort', 'ASC')->get();
        }
        if (in_array('shifts', $requestedData)) {
            $data['shifts'] = Shift::select('id', 'name')->get();
        }
        if (in_array('visit_types', $requestedData)) {
            $data['visit_types'] = VisitType::select('id', 'name')->get();
        }
        if (in_array('planSetting', $requestedData)) {
            $data['planSetting'] = PlanSetting::where('key', 'plan_shift')->value('value');
        }
        if (in_array('freqTypes', $requestedData)) {
            $freqType = OtherSetting::where('key', 'type_of_frequency')->value('value');
            $data['freqTypes'] = FrequencyType::select('id', 'name')->where('id', $freqType)->get();
        }

        if (in_array('requestTypes', $requestedData)) {
            $data['requestTypes'] = RequestType::select('id', 'name')->get();
        }
        if (in_array('salesTypes', $requestedData)) {
            $data['salesTypes'] = SalesTypes::select('id', 'name')->get();
        }
        if (in_array('months', $requestedData)) {
            $currentMonth = Carbon::now()->startOfMonth();
            $data['months'] = [$currentMonth->toDateString(), Carbon::parse($currentMonth)->addMonth(1)->startOfMonth()->toDateString()];
            $data['current_month'] = $currentMonth->format('M-Y');
        }
        if (in_array('listSetting', $requestedData)) {
            $data['listSetting'] = ListSetting::where('key', 'list_type')->value('value') == 'Doctor';
        }
        return response()->json(['data' => $data]);
    }
    public function getLineData(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $from = $request->from ? Carbon::parse($request->from)->startOfDay() : Carbon::now();
        $to = $request->to ? Carbon::parse($request->to)->endOfDay() : Carbon::now();
        $requestedData = $request->data;
        $data = [];
        $lines = Line::whereIntegerInRaw('id', $request->lines)->get();
        if (in_array('divisions', $requestedData)) {
            $data['divisions'] = [];
            foreach ($lines as $line) {
                $divisions = $user->userDivisions($line, $from, $to)->toArray();
                $data['divisions'] = array_merge($data['divisions'], $divisions);
            }
        }
        if (in_array('divisions', $requestedData) && $request['action']) {
            $data['divisions'] = [];
            if ($request['action'] == 1) {
                foreach ($lines as $line) {
                    $divisions = $user->userDivisions($line, $from, $to)->where('is_kol', 0)->toArray();
                    $data['divisions'] = array_merge($data['divisions'], $divisions);
                }
            } else {
                foreach ($lines as $line) {
                    $divisions = $user->divisions($from, $to)->where('is_kol', 1)
                        ->where('line_divisions.line_id', $line->id)->get()->toArray();
                    $data['divisions'] = array_merge($data['divisions'], $divisions);
                }
            }
        }
        if (in_array('last_level_divisions', $requestedData)) {
            $data['last_level_divisions'] = [];
            $division_type = DivisionType::where('last_level', '=', 1)->value('id');
            foreach ($lines as $line) {
                $last_level_divisions = $user->userDivisions($line)
                    ->where('division_type_id', $division_type)->toArray();
                $data['last_level_divisions'] = array_merge($data['last_level_divisions'], $last_level_divisions);
            }
        }
        if (in_array('users', $requestedData)) {
            $data['users'] = $user->belowUsersOfAllLinesWithPositions($lines, 'Active', $from, $to);
            if ($request->is_mr) {
                $data['users'] = User::select([
                    DB::raw('distinct crm_users.id as id'),
                    'users.fullname as fullname',
                    'lines.name as line',
                ])
                    ->leftJoin('line_users', function ($join) {
                        $join->on('users.id', 'line_users.user_id')
                            ->where(
                                fn($q) => $q->where('line_users.to_date', '>=', (string)\Illuminate\Support\Carbon::now())
                                    ->orWhere('line_users.to_date', null)
                            );
                    })
                    ->leftJoin('lines', 'line_users.line_id', 'lines.id')
                    ->leftJoin('line_users_divisions', function ($join) {
                        $join->on('users.id', 'line_users_divisions.user_id')
                            ->where(
                                fn($q) => $q->where('line_users_divisions.to_date', '>=', (string)Carbon::now())
                                    ->orWhere('line_users_divisions.to_date', null)
                            );
                    })
                    ->leftJoin('line_divisions', 'line_users_divisions.line_division_id', 'line_divisions.id')
                    ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
                    ->where('status', 'active')
                    ->whereIntegerInRaw('lines.id', $lines->pluck('id')->toArray())
                    ->where('division_types.last_level', 1)
                    ->where("line_divisions.is_kol", 0)
                    ->whereIn("users.id", $user->pluck("id")->toArray())
                    ->get()->toArray();
            }
            $data['codes'] = collect($data['users'])->filter(fn($user) => $user->emp_code != null)->values();
        }
        if (in_array('allUsers', $requestedData)) {
            $allUsers = collect([]);
            $positions = UserPosition::with('user')->where('user_positions.from_date', '<=', $from?->toDateString())
                ->where(
                    fn($q) => $q->where('user_positions.to_date', '>=', $to?->toDateString())
                        ->orWhere('user_positions.to_date', null)
                )->get()->pluck('user');
            $lines = Line::select('id', 'name')->whereIntegerInRaw('id', $request->lines)->get();
            foreach ($lines as $line) {
                $divisions = $line->divisions($from, $to)->orderBy('division_type_id', 'ASC')->get();
                $allUsers = $allUsers->merge(LineDivisionUser::whereIntegerInRaw('line_division_id', $divisions)
                    ->with('user')->get()->pluck('user'));
            }
            $allUsers = $allUsers->merge($positions);
            $data['allUsers'] = $allUsers->unique('id')->values();
        }

        if (in_array('specialities', $requestedData)) {
            $data['specialities'] = Speciality::select('id', 'name')->whereHas(
                'lineSpecialities',
                fn($q) => $q->whereIntegerInRaw('line_specialities.line_id', $request->lines)
                    ->where('line_specialities.from_date', '<=', $from?->toDateString() ?? (string)Carbon::now())
                    ->where(
                        fn($q) => $q->where('line_specialities.to_date', '>=', $to->toDateString() ?? (string)Carbon::now())
                            ->orWhere('line_specialities.to_date', null)
                    )
            )->get();
        }
        if (in_array('bricks', $requestedData)) {
            $data['bricks'] = Brick::select('id', 'name')->whereHas(
                'lineBricks',
                fn($q) => $q->whereIntegerInRaw('line_bricks.line_id', $request->lines)
                    ->where('line_bricks.from_date', '<=', $from?->toDateString() ?? (string)Carbon::now())
                    ->where(
                        fn($q) => $q->where('line_bricks.to_date', '>=', $to?->toDateString() ?? (string)Carbon::now())
                            ->orWhere('line_bricks.to_date', null)
                    )
            )->get();
        }
        if (in_array('products', $requestedData)) {
            if ($request->prductType) {
                $data['products'] = LineProduct::whereIntegerInRaw('line_id', $lines)
                    ->where(function ($query) use ($from, $to) {
                        $query->where(function ($subQuery) use ($from, $to) {
                            $subQuery->whereNull('line_products.to_date') // Active records
                                ->orWhereBetween('line_products.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                                ->orWhere('line_products.to_date', '>=', $to->toDateString()); // Ends within range
                        })
                            ->where(function ($subQuery) use ($from, $to) {
                                $subQuery->where('line_products.from_date', '<=', $from->toDateString()) // Starts before range
                                    ->orWhereBetween('line_products.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                            });
                    });
                return match ($request->prductType) {
                    1 => $data['products']->whereHas('product', fn($q) => $q->where('products.is_hidden', 0))
                        ->with('product')
                        ->get()
                        ->pluck('product'),
                    2 => $data['products']->whereHas('product', fn($q) => $q->where('products.is_hidden', 0))
                        ->with('product.brands')
                        ->get()
                        ->pluck('product.brands')
                        ->collapse()
                        ->unique('id')
                        ->values(),
                    3 => $data['products']->whereHas('product', fn($q) => $q->where('products.is_hidden', 1))
                        ->with('product')
                        ->get()
                        ->pluck('product'),
                    default => $data['products']
                        ->with('product')
                        ->get()
                        ->pluck('product')
                };
            } else {
                $data['products'] = Product::select('id', 'name')->whereHas(
                    'allProducts',
                    fn($q) => $q->whereIntegerInRaw('line_products.line_id', $request->lines)
                        ->where(function ($query) use ($from, $to) {
                            $query->where(function ($subQuery) use ($from, $to) {
                                $subQuery->whereNull('line_products.to_date') // Active records
                                    ->orWhereBetween('line_products.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                                    ->orWhere('line_products.to_date', '>=', $to->toDateString()); // Ends within range
                            })
                                ->where(function ($subQuery) use ($from, $to) {
                                    $subQuery->where('line_products.from_date', '<=', $from->toDateString()) // Starts before range
                                        ->orWhereBetween('line_products.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                                });
                        })
                )->get();
            }
        }
        if (in_array('productBudgetCommercial', $requestedData)) {
            $data['productBudgetCommercial'] = Product::whereHas(
                'lineproducts',
                fn($q) => $q->whereIntegerInRaw('line_products.line_id', $request->lines)
                    ->where('line_products.from_date', '<=', $from?->toDateString() ?? (string)Carbon::now())
                    ->where(
                        fn($q) => $q->where('line_products.to_date', '>=', $to?->toDateString() ?? (string)Carbon::now())
                            ->orWhere('line_products.to_date', null)
                    )
            )
                ->whereHas(
                    'budget',
                    fn($q) => $q
                        ->whereIntegerInRaw('budgets.div_id', collect($data['divisions'])->pluck('id'))
                        ->whereYear('budgets.from_date', '=', $from?->toDateString() ?? (string)Carbon::now())
                )->get();
        }
        if (in_array('productSamples', $requestedData)) {
            $data['productSamples'] = Product::whereHas(
                'lineproducts',
                fn($q) => $q->whereIntegerInRaw('line_products.line_id', $request->lines)
                    ->where('line_products.from_date', '<=', $from?->toDateString() ?? (string)Carbon::now())
                    ->where(
                        fn($q) => $q->where('line_products.to_date', '>=', $to?->toDateString() ?? (string)Carbon::now())
                            ->orWhere('line_products.to_date', null)
                    )
            )
                ->whereHas(
                    'samples',
                    // fn($q) => $q
                    // ->whereIntegerInRaw('samples.div_id', collect($data['divisions'])->pluck('id'))
                    // ->whereYear('budgets.from_date', '=', $from?->toDateString() ?? (string)Carbon::now())
                )->get();
        }
        if (in_array('classes', $requestedData)) {
            $data['classes'] = Classes::select('id', 'name')->whereHas(
                'lineClasses',
                fn($q) => $q->whereIntegerInRaw('line_classes.line_id', $request->lines)
                    ->where('line_classes.from_date', '<=', $from?->toDateString() ?? (string)Carbon::now())
                    ->where(
                        fn($q) => $q->where('line_classes.to_date', '>=', $to?->toDateString() ?? (string)Carbon::now())
                            ->orWhere('line_classes.to_date', null)
                    )
            )->get();
        }
        if (in_array('giveaways', $requestedData)) {
            $data['giveaways'] = Giveaway::select('id', 'name')->whereIntegerInRaw('line_id', $request->lines)->get();
        }
        if (in_array('distributors', $requestedData)) {
            $data['distributors'] = Line::lineDistributors($lines->pluck('id'));
        }

        if (in_array('accounts', $requestedData)) {
            $data['accounts'] = Account::whereHas(
                'lines',
                fn($q) => $q->whereIntegerInRaw('account_lines.line_id', $request->lines)
                    ->where('account_lines.from_date', '<=', $from?->toDateString() ?? (string)Carbon::now())
                    ->where(
                        fn($q) => $q->where('account_lines.to_date', '>=', $to?->toDateString() ?? (string)Carbon::now())
                            ->orWhere('account_lines.to_date', null)
                    )
            )->get();
        }

        if (in_array('accountTypes', $requestedData)) {
            $data['accountTypes'] = AccountType::select('id', 'name')->orderBy('sort', 'ASC')->get();
        }

        if (in_array('coachingTypes', $requestedData)) {
            $data['coachingTypes'] = Type::where(
                fn($query) =>
                Type::whereNull('line_id')->exists()
                    ? $query
                    : $query->whereIntegerInRaw('line_id', $request->lines)
            )->get();
        }

        return response()->json([
            'role' => $user,
            'data' => $data,
        ]);
    }
}
