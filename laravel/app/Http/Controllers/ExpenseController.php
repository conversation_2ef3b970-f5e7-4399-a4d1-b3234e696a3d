<?php

namespace App\Http\Controllers;

use App\AccountType;
use App\ActualVisit;
use App\ActualVisitSetting;
use App\DivisionType;
use App\Exceptions\CrmException;
use App\Helpers\LogActivity;
use App\Helpers\Notifications\NotificationHelper;
use App\Http\Requests\ExpenseRequest;
use App\LogActivity as AppLogActivity;
use App\Models\ApprovalSetting;
use App\Models\Attachment;
use App\Models\CommercialRequest\CommercialSetting;
use App\Models\Expenses\Distance;
use App\Models\Expenses\Expense;
use App\Models\Expenses\ExpenseDetails;
use App\Models\Expenses\ExpenseDivision;
use App\Models\Expenses\ExpenseProduct;
use App\Models\Expenses\PerLocation\ExpenseLocationPrice;
use App\Models\Expenses\PerLocation\ExpenseLocationSetting;
use App\Models\Expenses\PerLocation\ExpensePriceFactor;
use App\Models\Expenses\Types\ExpenseType;
use App\Models\ExpenseSetting;
use App\Models\Meal;
use App\Models\PaidRequest;
use App\Models\Policy;
use App\Models\RequestFeedback;
use App\Models\StartExpenseMonth;
use App\Notifications\ExpenseCreatedNotification;
use App\OwActualVisit;
use App\PlanVisitDetails;
use App\Position;
use App\Role;
use App\Setting;
use App\StartPlanDay;
use App\User;
use App\Vacation;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ExpenseController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $users = $user->indexPerUser($user);
        $expenses = Expense::select(
            'expenses.id as id',
            DB::raw('DATE_FORMAT(crm_expenses.date,"%Y-%m") as date'),
            'lines.name as line',
            'lines.id as line_id',
            'expense_users.fullname as Emp',
            'expense_users.emp_code as emp_code',
            'expenses.amount as total',
            DB::raw("
                IFNULL(
                    GROUP_CONCAT(
                        DISTINCT CASE 
                            WHEN (
                                SELECT 
                                    MAX(crm_division_types.last_level) 
                                FROM crm_division_types 
                                WHERE crm_division_types.id = ANY_VALUE(crm_manager_divisions.division_type_id)
                            ) = 1 THEN crm_last_level_divisions.name
                            ELSE crm_manager_divisions.name
                        END
                    ), 
                    ''
                ) as division
            "),
            // DB::raw('Max(crm_division_types.last_level) as dt'),
            DB::raw('IFNULL(group_concat(distinct crm_log_users.name),"") as edited_by'),
            'plan_visit_details.approval as status'

        )
            ->leftJoin('users as expense_users', 'expenses.user_id', 'expense_users.id')
            ->leftJoin('line_users_divisions', function ($join) {
                $join->on('expense_users.id', 'line_users_divisions.user_id')
                    ->where("line_users_divisions.from_date", "<=", (string)Carbon::now())
                    ->whereNull('line_users_divisions.deleted_at')
                    ->where(fn($q) => $q->where('line_users_divisions.to_date', '>=', (string)Carbon::now())
                        ->orWhere('line_users_divisions.to_date', null));
            })
            ->leftJoin('line_divisions as manager_divisions', function ($join) {
                $join->on('line_users_divisions.line_division_id', 'manager_divisions.id')
                    ->whereNull('manager_divisions.deleted_at')
                    ->where('manager_divisions.is_kol', 0);
            })
            ->leftJoin('lines', 'expenses.line_id', 'lines.id')
            ->leftJoin('expense_divisions', 'expenses.id', 'expense_divisions.expense_id')
            ->leftJoin('line_divisions as last_level_divisions', 'expense_divisions.div_id', 'last_level_divisions.id')
            ->leftJoin('division_types', 'manager_divisions.division_type_id', 'division_types.id')
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('expenses.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', Expense::class);
                }
            )
            ->leftJoin(
                'log_activities',
                function ($join) {
                    $join->on('expenses.id', '=', 'log_activities.model_id');
                    $join->where('log_activities.model_type', Expense::class)->where('action_id', 4);
                }
            )
            ->leftJoin('users as log_users', 'log_activities.user_id', 'log_users.id')
            ->leftJoin(
                'paid_requests',
                function ($join) {
                    $join->on('expenses.id', '=', 'paid_requests.paidable_id');
                    $join->where('paid_requests.paidable_type', Expense::class);
                }
            );
        if (count($users) > 0) {
            $expenses = $expenses->whereIntegerInRaw('expenses.user_id', $users->values());
        }
        if (
            request('query') == "Approved" || request('query') == "approved"
        ) {
            $expenses = $expenses->where('plan_visit_details.approval', 1);
        } elseif (
            request('query') == "Disapproved" || request('query') == "disapproved"
        ) {
            $expenses = $expenses->where('plan_visit_details.approval', 0);
        } elseif (
            request('query') == "Pending" || request('query') == "pending"
        ) {
            $expenses = $expenses->whereNull('plan_visit_details.approval');
        } else {
            $expenses = $expenses
                ->where(fn($q) => $q->where('expenses.id', 'Like', '%' . request('query') . '%')
                    ->orWhere('expense_users.fullname', 'Like', '%' . request('query') . '%')
                    ->orWhere('expense_users.emp_code', 'Like', '%' . request('query') . '%')
                    ->orWhere('expenses.date', 'Like', '%' . request('query') . '%')
                    ->orWhere('lines.name', 'Like', '%' . request('query') . '%')
                    ->orWhere('manager_divisions.name', 'Like', '%' . request('query') . '%')
                    ->orWhere('log_users.name', 'Like', '%' . request('query') . '%'));
        }

        if (request('columnFilter')) {


            foreach (request('columnFilter') as $key => $value) {
                $newKey = match ($key) {
                    'id' => 'expenses.id',
                    'emp' => 'expense_users.fullname',
                    'division' => 'manager_divisions.name',
                    'line' => 'lines.name',
                    'date' => 'expenses.date',
                    'status' => 'plan_visit_details.approval',
                    default => null,
                };
                if ($newKey != null) {
                    $expenses->where($newKey, "like", '%' . $value . '%');
                }
            }
        }
        $expenses = $expenses->groupBy('expenses.id', "plan_visit_details.approval")->orderBy('id', 'Desc')->simplePaginate(300);

        LogActivity::addLog();
        return $this->respond($expenses);
    }
    public function minDate()
    {
        $expenseWithVisits = ExpenseSetting::where('key', 'expense_time_work_with_visits')->value('value') == 'Yes';
        $min_date = Carbon::now()->toDateString();
        $max_date = Carbon::now()->toDateString();
        $min_header_date = Carbon::now()->toDateString();
        $max_header_date = Carbon::now()->toDateString();
        $expenseTime = null;
        // Details Date
        if ($expenseWithVisits) {
            $actual_start_day_value = ActualVisitSetting::where('key', 'actual_start_day')->first();
            $actual_start_day = StartPlanDay::where('name', $actual_start_day_value->value)->first();
            $min_date = Carbon::today()->addDays($actual_start_day->day)->toDateString();
            $max_date = Carbon::now()->toDateString();
            $min_header_date = Carbon::parse($min_date)->firstOfMonth()->toDateString();
            $max_header_date = Carbon::parse($max_date)->endOfMonth()->toDateString();
            $expenseTime = 1;
        } else {
            $expenseSetting = ExpenseSetting::where('key', 'expense_time')->value('value');
            $expenseTime = StartExpenseMonth::where('name', $expenseSetting)->value('day');
            // throw new CrmException($expenseTime);
            $min = Carbon::now()->addMonth($expenseTime)->firstOfMonth();
            $max = Carbon::now()->addMonth(-1)->endOfMonth();
            $min_date = Carbon::parse($min)->toDateString();
            $max_date = Carbon::parse($max)->toDateString();
            // header Date
            $expenseHeaderSetting = ExpenseSetting::where('key', 'expense_header_time')->value('value');
            $expenseHeaderTime = StartExpenseMonth::where('name', $expenseHeaderSetting)->value('day');
            $minHeader = Carbon::now()->addMonth($expenseHeaderTime)->firstOfMonth();
            $maxHeader = Carbon::now()->addMonth(-1)->endOfMonth();
            $min_header_date = Carbon::parse($minHeader)->toDateString();
            $max_header_date = Carbon::parse($maxHeader)->toDateString();
        }
        return $this->respond([
            'min_date' => $min_date,
            'max_date' => $max_date,
            'min_header_date' => $min_header_date,
            'max_header_date' => $max_header_date,
            'expenseTime' => $expenseTime
        ]);
    }
    public function policies()
    {
        $policies = Policy::where('policiable_type', Expense::class)->get();
        $setting = ExpenseSetting::where('key', 'choose_expense_type')->value('value');
        return $this->respond(['policies' => $policies, 'setting' => $setting]);
    }
    private function getStatus($expense)
    {
        if ($expense->approvals?->approval === null) return null;
        if ($expense->approvals?->approval === 1) return 1;
        if ($expense->approvals?->approval === 0) return 0;
    }
    public function expenseFlow(Request $request)
    {
        $expense = Expense::find($request->id);
        $approvalFlow = $expense->approvals?->approvalFlows()->count();
        $data = collect([]);
        if ($approvalFlow > 0) {
            $data = $data->merge($expense->approvals?->approvalFlows()->get());
        } else {
            $data = $data->push($expense->approvals);
        }
        $data = $data->map(function ($detail) use ($expense) {
            return [
                'id' => $detail?->id ?? null,
                // 'expense_id' => $expense?->id ?? null,
                'status' => $detail?->approval ?? null,
                'reason' => $detail?->approval == 0 ? $expense->reasons->first()?->reason : "",
                'approved_by' => $detail?->user?->fullname ?? '',
                'position' => $detail?->user?->menuroles ?? '',
                'date' => is_null($detail?->approval) ? "" : Carbon::parse($detail?->updated_at)->toDateString(),
                // 'actions' => ''

            ];
        });
        return $this->respond($data);
    }

    public function details(Request $request)
    {
        $details =  ExpenseDetails::where('expense_id', $request->id)->orderBy('date', 'ASC')->get()->map(function ($expense) {
            return [
                'id' => $expense->id,
                'type' => $expense->type?->name ?? '',
                'date' => $expense->date ?? '',
                'from' => $expense->location->source->name ?? '',
                'to' => $expense->location->destination->name ?? '',
                'distance' => round($expense->distance, 0) == 0 ? '' : round($expense->distance, 0) . ' Km',
                'value' => $expense->location->price ?? '',
                'meal' => $expense->meal->name ?? '',
                'meal_price' => $expense->meal->price ?? '',
                'amount' => $expense->amount ?? '',
                'brick' => ActualVisit::where('user_id', $expense->expense?->user?->id)
                    ->whereDate('visit_date', Carbon::parse($expense->date)->toDateString())->first()?->brick?->name ?? '',
                'description' => $expense->description ?? '',
                'file' => Attachment::where('attachable_id', $expense->id)
                    ->where('attachable_type', ExpenseDetails::class)->get()->pluck('url') ?? '',
                'details_actions' => ''
            ];
        });
        return $this->respond($details);
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(ExpenseRequest $request)
    {
        $approvalSetting = ApprovalSetting::where('key', 'expense_approval_center_flow')->value('value');
        $autoApproved = CommercialSetting::where('key', 'auto_approved')->value('value') == 'Yes';
        // throw new CrmException($request->all());
        DB::transaction(function () use ($request, $approvalSetting, $autoApproved) {
            /**@var User */
            $user = Auth::user();
            $hasApprovable = 1;
            if ($autoApproved) {
                $line = $user->lines()->first();
                $hasApprovable = $user->division($line)?->divisionType?->planable?->where('line_id', $line->id)->first()?->approvables()?->wherePivot('request_type', Vacation::class)->count() > 0;
                if ($user->hasPosition()) {
                    $hasApprovable = $user->position()->planable
                        ->first()->approvables()->wherePivot('request_type', Expense::class)->count() > 0;
                }
            }
            $totalAmount = 0.0;
            $expense = Expense::create([
                'user_id' => Auth::id(),
                'line_id' => $request->line,
                'date' => $request->date,
            ]);
            foreach ($request->divisions as $division) {
                ExpenseDivision::create([
                    'expense_id' => $expense->id,
                    'div_id' => $division
                ]);
            }
            foreach ($request->products as $product) {
                ExpenseProduct::create([
                    'expense_id' => $expense->id,
                    'product_id' => $product
                ]);
            }

            foreach ($request->details as $detail) {
                if ($approvalSetting == 'No') {
                    $exists = Expense::where('user_id', $user->id)->whereHas('approvals', function ($q) {
                        $q->where(fn($q) => $q->whereNull('approval')->orWhere('approval', 1));
                    })->whereHas('details', function ($q) use ($detail) {
                        $q->whereDate('date', Carbon::parse($detail['date'])->toDateString())->where('type_id', $detail['type']);
                    })->exists();
                    if ($exists) throw new Exception("Expense Has Date Already Been Taken At This Expense Type");
                } else {
                    $expenses = Expense::where('user_id', $user->id)->whereHas('approvals', function ($q) {
                        $q->where(fn($q) => $q->whereNull('approval')->orWhere('approval', 1))
                            ->whereHas('approvalFlows', function ($query) {
                                $query->where(fn($q) => $q->whereNull('approval')->orWhere('approval', 1));
                            });
                    })->whereHas('details', function ($q) use ($detail) {
                        $q->whereDate('date', Carbon::parse($detail['date'])->toDateString())->where('type_id', $detail['type']);
                    })->get();
                    foreach ($expenses as $expense) {
                        if ($expense->approvals->approvalFlows->first()) {
                            $flow = $expense->approvals->approvalFlows()->orderBy('id', 'ASC')->first();
                            if ($flow->approval == 1)
                                throw new Exception("Expense Has Date Already Been Taken At This Expense Type");
                        } else {
                            throw new Exception("Expense Has Date Already Been Taken At This Expense Type");
                        }
                    }
                }


                $is_location = ExpenseType::where('id', $detail['type'])->where('is_location', 1)->exists();
                if ($is_location && $detail['locationId'] == null)  throw new Exception("Invalid Locations");
                if ($is_location && $detail['locationId'] && $detail['meal']) {
                    $detail['amount'] = ExpenseLocationPrice::find($detail['locationId'])->price + Meal::find($detail['meal']['id'])->price;
                }
                $totalAmount += $detail['amount'];
                $record = ExpenseDetails::create([
                    'expense_id' => $expense->id,
                    'date' => new Carbon($detail['date']),
                    'type_id' => $detail['type'],
                    'expense_price_id' => $detail['locationId'] ?? null,
                    'meal_id' => !isNullable($detail['meal']) ? $detail['meal']['id'] : null,
                    'distance' => $detail['distance'],
                    'amount' => $detail['amount'],
                    'description' => $detail['description'],
                ]);
                foreach ($detail['attachments'] as $attachment) {
                    Attachment::create([
                        'attachable_id' => $record->id,
                        'attachable_type' => ExpenseDetails::class,
                        'path' => $attachment,
                    ]);
                }
            }
            // throw new CrmException((float)$totalAmount);
            foreach ($request->attachments as $attachment) {
                Attachment::create([
                    'attachable_id' => $expense->id,
                    'attachable_type' => Expense::class,
                    'path' => $attachment,
                ]);
            }

            $locationWithTicket = ExpenseType::where('location_with_ticket', '=', 1)->value('id');
            if ($locationWithTicket) {
                $expenseTypeLocation = ExpenseType::where('is_location', 1)->value('id');
                $ticketExpenses = ExpenseDetails::where('expense_id', $expense?->id)->where('type_id', $locationWithTicket)->get();
                if (count($ticketExpenses) > 0) {
                    foreach ($ticketExpenses as $ticketExpense) {
                        $locationExpenseAtTicketDate =  ExpenseDetails::where('expense_id', $expense->id)->where('type_id', $expenseTypeLocation)
                            ->where('date', $ticketExpense->date)->first();
                        if ($locationExpenseAtTicketDate) {
                            $locationPrice = ExpenseLocationPrice::find($locationExpenseAtTicketDate->expense_price_id)?->price;
                            $locationExpenseAtTicketDate->update(['amount' => ($locationExpenseAtTicketDate->amount - $locationPrice)]);
                            $totalAmount -= $locationPrice;
                        }
                    }
                }
            }
            $expense->update(['amount' => (float)$totalAmount]);

            PlanVisitDetails::firstOrCreate([
                'visitable_id' => $expense->id,
                'visitable_type' => Expense::class,
                'approval' => $hasApprovable ?  null : 1
            ]);

            PaidRequest::firstOrCreate([
                'paidable_id' => $expense->id,
                'paidable_type' => Expense::class,
            ], [
                'description' => null,
                'amount' => $totalAmount,
            ]);



            NotificationHelper::send(
                collect($user->approvableUsers($expense->line_id)),
                new ExpenseCreatedNotification('Expense Created', auth()->user())
            );
        });

        LogActivity::addLog();


        return response()->json(['message' => 'success']);
    }


    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $is_brand_level = Setting::where('key', 'reports_level')->value('value') == 'Brand';
        $expense = Expense::where('id', $id)->get()->map(function ($expense) use ($is_brand_level) {
            $products = Expense::getExpenseBrands($expense, $is_brand_level);
            return [
                'id' => $expense->id,
                'Line' => $expense->line->name,
                'User' => $expense->user->fullname,
                'user_id' => $expense->user->id,
                'Date' => Carbon::parse($expense->date)->toDateString(),
                'Products' => !empty($products) ? $products->pluck('name')->implode(' , ') : '',
                // 'products' => $expense->getExpenseProducts ? $expense->getExpenseProducts->pluck('name')->implode(' , ') : '',
                'Divisions' => $expense->getExpenseDivisions ? $expense->getExpenseDivisions->pluck('name')->implode(' , ') : '',
                'Total Amount' => $expense->amount,
            ];
        })->collapse();

        $editable_expense = Expense::with('line', 'details', 'attachments', 'getExpenseDivisions', 'getExpenseProducts', 'details.type')->where('id', $id)->get()[0];
        $divisions = ExpenseLocationSetting::select('id', 'name')->get();
        /**@var User */
        $user = User::find($expense['user_id']);
        $role = explode(',', $user->menuroles)[0];
        $roleId = Role::where('name', $role)->value('id');
        $date = Carbon::parse($editable_expense->details->first()->date)->format('Y-m');
        $meals = Meal::where(DB::raw("(DATE_FORMAT(from_date,'%Y-%m'))"), '<=', $date)
            ->where(fn($q) => $q->where(DB::raw("(DATE_FORMAT(to_date,'%Y-%m'))"), '>=', $date)->orWhere('to_date', '=', null))
            ->where('role_id', $roleId)->get();
        $details = $editable_expense->details->map(function ($detail) {
            return [
                'id' => $detail->id,
                'amount' => $detail->amount,
                'date' => Carbon::parse($detail->date)->toDateString(),
                'description' => $detail->description,
                'type' => $detail->type,
                'from' => $detail->location?->source?->id,
                'to' => $detail->location?->destination?->id,
                'km_price' => $detail->location?->price,
                'meal' => $detail->meal,
                'meal_price' => $detail->meal?->price,
                'is_location' => $detail->type?->is_location,
            ];
        })->collect();
        $accountTypes =  AccountType::select('id', 'name')->pluck('id')->toArray();
        $expense_details = ExpenseDetails::where('expense_id', $id)->get()->map(function ($expense) use ($user, $accountTypes) {
            $date = Carbon::parse($expense->date)->toDateString();
            $month = Carbon::parse($expense->date)->format('m');
            $year = Carbon::parse($expense->date)->toDateString('Y');
            return [
                'id' => $expense->expense_id,
                'type' => $expense->type?->name ?? '',
                'user_id' => $user->id ?? '',
                'date' => $expense->date ?? '',
                'from' => $expense->location?->source?->name ?? '',
                'to' => $expense->location?->destination?->name ?? '',
                'distance' => $expense->distance ?? '',
                'amount' => $expense->amount ?? '',
                'meal' => $expense->meal->name ?? '',
                'meal_price' => $expense->meal->price ?? '',
                'description' => $expense->description ?? '',
                'brick' => ActualVisit::where('user_id', $expense->expense?->user?->id)
                    ->whereDate('visit_date', Carbon::parse($expense->date)->toDateString())->first()?->brick?->name ?? '',
                'file' => Attachment::where('attachable_id', $expense->id)
                    ->where('attachable_type', ExpenseDetails::class)->get()->pluck('url') ?? '',
                'actions' => $user->hasVisits($date, 'acc_type_id', $accountTypes)
                    ? 'visit' : ($user->hasOw($date)
                        ? 'ow' : ($user->hasVacations($date, null, $month, $year)
                            ? 'vacation' : 0)),
            ];
        });
        $attachments = Attachment::where('attachable_id', $id)->where('attachable_type', Expense::class)->get()->map(function ($expense) {
            return [
                'id' => $expense->attachable_id,
                'file' => $expense->path,
            ];
        });

        $feedbacks = RequestFeedback::where('requestable_id', $id)->where('requestable_type', Expense::class)->get()->map(function ($feedback) {
            return [
                'id' => $feedback->id,
                'request_id' => $feedback->requestable_id,
                'request_type' => $feedback->requestable_type,
                'user' => $feedback->user->fullname,
                'feedback' => $feedback->feedback,
                'auth' => $feedback->user->id == Auth::id() ? 1 : 0,
            ];
        });
        $paids = PaidRequest::where('paidable_id', $id)->where('paidable_type', Expense::class)->get()->map(function ($paid) {
            return [
                'id' => $paid->id,
                'paid_id' => $paid->paidable_id,
                'paid_type' => $paid->paidable_type,
                'user' => $paid->user?->fullname ?? '',
                'paid' => $paid->type,
                'amount' => $paid->amount ?? '',
                'description' => $paid->description ?? '',
                'ref_no' => $paid->ref_no ?? '',
                'date' => $paid->date ?? '',
            ];
        });
        $expenseApprove = Expense::find($id);
        $approvalFlow = $expenseApprove->approvals?->approvalFlows()->count();
        $approvals = collect([]);
        if ($approvalFlow > 0) {
            $approvals = $approvals->merge($expenseApprove->approvals?->approvalFlows()->get());
        } else {
            $approvals = $approvals->push($expenseApprove->approvals);
        }
        $approvals = $approvals->map(function ($detail) use ($expenseApprove) {
            return [
                'id' => $detail?->id ?? null,
                'status' => $detail?->approval ?? null,
                'reason' => $detail?->approval == 0 ? $expenseApprove->reasons->first()?->reason : "",
                'approved_by' => $detail?->user?->fullname ?? '',
                'position' => $detail?->user?->menuroles ?? '',
                'date' => is_null($detail?->approval) ? "" : Carbon::parse($detail?->updated_at)->toDateTimeString(),
            ];
        });
        $model_id = $id;
        $model_type = Expense::class;
        LogActivity::addLog($model_id, $model_type);
        return response()->json([
            'expense' => $expense,
            'divisions' => $divisions,
            'meals' => $meals,
            'attachments' => $attachments,
            'expense_details' => $expense_details,
            'editable_expense' => $editable_expense,
            'details' => $details,
            'feedbacks' => $feedbacks,
            'paids' => $paids,
            'approvals' => $approvals,
        ]);
    }

    public function getUsersData(Request $request)
    {
        $user = User::find($request['user_id']);
        $data = collect([]);
        if ($request['actions'] == 'visit') {
            $data = ActualVisit::where('user_id', $user->id)->whereDate('visit_date', Carbon::parse($request['date'])->toDateString())->get()->map(function ($actual) {
                return [
                    'id' => $actual->id,
                    'date' => $actual->visit_date,
                    'user' => $actual->user->fullname,
                    'type' => $actual->visitType->name,
                    'line' => $actual->line->name,
                    'division' => $actual->division->name,
                    'account' => $actual->account?->name,
                    'account_id' => $actual->account?->id,
                    'doctor' => $actual->doctor != null ? $actual->doctor->name : "",
                    'doctor_id' => $actual->doctor != null ? $actual->doctor->id : "",
                    'speciality' => $actual->doctor != null ? $actual->doctor->speciality->name : "",
                    'acc_type' => $actual->account?->type != null ? $actual->account?->type?->name : "",
                    'shift' => $actual->account?->type?->shift != null ? $actual->account?->type->shift?->name : "",
                ];
            });
        }
        if ($request['actions'] == 'ow') {
            $data = OwActualVisit::where('user_id', $user->id)->whereDate('date', Carbon::parse($request['date'])->toDateString())->get()->map(function ($officework) {
                return [
                    'id' => $officework->id,
                    'type' => $officework->owType?->name,
                    'date' => Carbon::parse($officework->date)->toDateString(),
                    'user' => $officework->user->fullname,
                    'shift' => $officework->shift != null ? $officework->shift?->name : "Full Day",
                    'notes' => $officework->notes ?? "",
                ];
            });
        }
        if ($request['actions'] == 'vacation') {
            $month = Carbon::parse($request['date'])->format('m');
            $year = Carbon::parse($request['date'])->format('Y');
            $data = Vacation::where('user_id', $user->id)->whereDate('date', Carbon::parse($request['date'])->toDateString())
                ->whereMonth("from_date", $month)
                ->whereYear("from_date", $year)
                ->whereHas('details', fn($q) => $q->where('approval', 1))->get()->map(function ($vacation) {
                    return [
                        'id' => $vacation->id,
                        'user' => $vacation->user->fullname,
                        'from_date' => Carbon::parse($vacation->from_date)->toDateString(),
                        'to_date' => Carbon::parse($vacation->to_date)->toDateString(),
                        'type' => $vacation->type->name,
                        'shift' => $vacation->shift ? $vacation->shift->name : 'Full Day',
                        'notes' => $vacation->notes ?? '',
                    ];
                });
        }

        return $this->respond($data);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // throw new  CrmException($request->all());
        DB::transaction(function () use ($request, $id) {
            $totalAmount = 0.0;
            $expense = Expense::find($id);
            $expense->update([
                'line_id' => is_array($request->expense['line']) ? $request->expense['line']['id'] : $request->expense['line'],
            ]);
            // throw new CrmException($request->product_ids);
            $expense->getExpenseProducts()->sync($request->product_ids);
            $expense->getExpenseDivisions()->sync($request->div_ids);
            foreach ($request->details as $key => $detail) {
                $totalAmount += $detail['amount'];
                $data[$key] =  [
                    'expense_id' => $expense->id,
                    'date' => new Carbon($detail['date']),
                    'type_id' => is_array($detail['type']) ? $detail['type']['id'] : $detail['type'],
                    'amount' => $detail['amount'],
                    'meal_id' => !isNullable($detail['meal']) ? $detail['meal']['id'] : null,
                    'description' => $detail['description'],
                ];
                $expense->details[$key]->update($data[$key]);
            }
            $expense->update(['amount' => (float)$totalAmount]);
        });
        $model_id = $id;
        $model_type = Expense::class;
        LogActivity::addLog($model_id, $model_type);
        return $this->respondSuccess();
    }

    public function updateDetailsDate(Request $request, $id)
    {
        if ($request->date) {
            Expense::find($request->expense['id'])->update(['date' => $request->date]);
            $model_id = $id;
            $model_type = Expense::class;
            LogActivity::addLog($model_id, $model_type);
            return $this->respondSuccess();
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $expense = Expense::find($id);
        // throw new CrmException($expense->attachments);
        $expense->details()->delete();
        $expense->divisions()->delete();
        $expense->products()->delete();
        $expense->attachments()->delete();
        $expense->delete();
        $model_id = $id;
        $model_type = Expense::class;

        LogActivity::addLog($model_id, $model_type);
        return $this->respondSuccess('Deleted Successfully');
    }
    public function resetApprovals($id)
    {
        throw new CrmException(PlanVisitDetails::find($id));
    }
    public function feedback($id, Request $request)
    {
        $expense = Expense::find($id);
        $user_id = Auth::id();
        $expense_feedback = new RequestFeedback();
        $expense_feedback->requestable_type = Expense::class;
        $expense_feedback->requestable_id = $expense->id;
        $expense_feedback->feedback = $request->feedback;
        $expense_feedback->user_id = $user_id;
        $expense_feedback->save();
        return $this->respondSuccess();
        // throw new CrmException($expense_feedback);

    }
    public function changeExpenseDates()
    {
        // throw new CrmException(Expense::where(DB::raw("(DATE_FORMAT(crm_expenses.date,'%m'))"), Carbon::now()->addMonth(-2)->format('m'))
        // ->where(DB::raw("(DATE_FORMAT(crm_expenses.date,'%Y'))"), Carbon::now()->addMonth(-2)->format('Y'))->get());
        ExpenseSetting::where('key', 'expense_header_time')->update(['value' => 'Now']);
        Expense::where(DB::raw("(DATE_FORMAT(crm_expenses.date,'%m'))"), Carbon::now()->addMonth(-1)->format('m'))
            ->where(DB::raw("(DATE_FORMAT(crm_expenses.date,'%Y'))"), Carbon::now()->addMonth(-1)->format('Y'))->whereHas('approvals', function ($q) {
                $q->whereNull('approval');
            })
            ->update(['date' => Carbon::now()->firstOfMonth()]);
        LogActivity::addLog();
        return $this->respondSuccess();
    }
}
