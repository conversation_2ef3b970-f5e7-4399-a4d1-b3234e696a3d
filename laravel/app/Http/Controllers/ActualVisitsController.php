<?php

namespace App\Http\Controllers;

use App\Account;
use App\AccountLines;
use App\AccountType;
use App\ActualVisit;
use App\ActualVisitSetting;
use App\AvRequiredInput;
use App\Http\Requests\ActualVisitRequest;
use App\PlanVisit;
use App\VisitFeedbacks;
use App\VisitGiveaway;
use App\VisitProduct;
use App\VisitType;
use App\ActualVisitManager;
use App\Doctor;
use App\Exceptions\CrmException;
use App\Helpers\LogActivity;
use App\Helpers\Notifications\NotificationHelper;
use App\Http\Requests\ActualVisitPerAccountRequest;
use App\Http\Requests\ImportRequest;
use App\Http\Requests\MailRequest;
use App\Line;
use App\LineBricks;
use App\Models\ActualDoubleFeedback;
use App\Models\ActualVisitedDoctor;
use App\Models\Attachment;
use App\Models\DoubleVisitLocation;
use App\Models\DoubleVisitType;
use App\Models\EDetailing\Presentation;
use App\Models\EDetailing\PresentationSlide;
use App\Models\EDetailing\Statistic;
use App\Models\ListType;
use App\Models\NewAccountDoctor;
use App\Models\OrderRequest;
use App\Models\OrderRequestDetail;
use App\Models\PharmacyTypeVisit;
use App\Models\PlanLevel;
use App\Models\Policy;
use App\Notifications\DoublePlanNotification;
use App\PlanVisitDetails;
use App\Product;
use App\ProductBrands;
use App\ProductSpecialities;
use App\Setting;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Exception;
use Maatwebsite\Excel\Excel as ExcelType;

class ActualVisitsController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $users = $user->indexPerUser($user);
        $actual_visits = ActualVisit::select(
            'actual_visits.id',
            'users.fullname as user',
            'actual_visits.visit_date as date',
            'lines.name as line',
            'line_divisions.name as division',
            'bricks.name as brick',
            'accounts.id as account_id',
            'accounts.name as account',
            'account_types.name as type',
            'doctors.id as doctor_id',
            'doctors.name as doctor',
            'specialities.name as speciality',
            'visit_types.name as visit_type',
            'actual_visits.plan_id'
        )
            ->leftJoin('lines', 'actual_visits.line_id', 'lines.id')
            ->leftJoin('users', 'actual_visits.user_id', 'users.id')
            ->leftJoin('line_divisions', 'actual_visits.div_id', 'line_divisions.id')
            ->leftJoin('bricks', 'actual_visits.brick_id', 'bricks.id')
            ->leftJoin('accounts', 'actual_visits.account_id', 'accounts.id')
            ->leftJoin('account_types', 'actual_visits.acc_type_id', 'account_types.id')
            ->leftJoin('doctors', 'actual_visits.account_dr_id', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('visit_types', 'actual_visits.visit_type_id', 'visit_types.id')
            ->where(fn($q) => $q->where('actual_visits.id', 'Like', '%' . request('query') . '%')
                ->orWhere('accounts.name', 'Like', '%' . request('query') . '%')
                ->orWhere('account_types.name', 'Like', '%' . request('query') . '%')
                ->orWhere('doctors.name', 'Like', '%' . request('query') . '%')
                ->orWhere('specialities.name', 'Like', '%' . request('query') . '%')
                ->orWhere('actual_visits.visit_date', 'Like', '%' . request('query') . '%')
                ->orWhere('visit_types.name', 'Like', '%' . request('query') . '%')
                ->orWhere('users.fullname', 'Like', '%' . request('query') . '%')
                ->orWhere('bricks.name', 'Like', '%' . request('query') . '%')
                ->orWhere('line_divisions.name', 'Like', '%' . request('query') . '%'));
        if (count($users) > 0) {
            $actual_visits = $actual_visits->whereIntegerInRaw('actual_visits.user_id', $users->values());
        }
        $actual_visits = $actual_visits->orderBy('visit_date', 'desc')->simplePaginate(300);
        LogActivity::addLog();
        return $this->respond($actual_visits);
    }

    public function getDoctorData(Doctor $doctor)
    {

        $followup = ActualVisit::where('account_dr_id', $doctor->id)
            ->where('user_id', Auth::id())
            ->with(['actualVisitProducts' => function ($q) {
                $q->whereNotNull('follow_up')->whereNull('follow_up_flag');
            }])->orderBy('visit_date', 'DESC')->limit(5)->get()->pluck('actualVisitProducts')->collapse()->values()->map(function ($visitProduct) {
                return [
                    'product' => $visitProduct->product?->name,
                    'product_id' => $visitProduct->product_id,
                    'visit_id' => $visitProduct->visit_id,
                    'follow_up' => $visitProduct->follow_up,
                    'flag' => 0,
                ];
            });
        $feedbacks = $feedbacks = ActualVisit::where('account_dr_id', $doctor->id)
            ->where('user_id', Auth::id())
            ->with('actualVisitProducts')->orderBy('visit_date', 'DESC')->limit(10)->get()->pluck('actualVisitProducts')->collapse()->values()->map(function ($visitProduct) {
                return [
                    'visit_id' => $visitProduct->visit_id,
                    'product' => $visitProduct->product?->name,
                    'product_id' => $visitProduct->product_id,
                    'feedback' => $visitProduct->feedback?->notes,
                ];
            });
        return $this->respond(['followup' => $followup, 'feedbacks' => $feedbacks]);
    }

    public function saveFollowUps(Request $request)
    {
        $last = $request->last;
        VisitProduct::where('visit_id', $last['visit_id'])->where('product_id', $last['product_id'])->update([
            'follow_up_flag' => $last['flag']
        ]);
        $savedFollowUp =  VisitProduct::where('visit_id', $last['visit_id'])->where('product_id', $last['product_id'])->first();
        return $this->respond($savedFollowUp);
    }
    public function getProductData($id)
    {
        $product_brand_level = ActualVisitSetting::where('key', 'actual_visit_level')->value('value');
        $product_messages = collect([]);
        $product_presentations = collect([]);
        if ($product_brand_level == 'Brand') {
            $product_messages = ProductBrands::where('brand_id', $id)->first()?->product->messages()->wherePivot('deleted_at', null)->get();
            $product_messages = !empty($product_messages) ?
                $product_messages : Product::find($id)->messages()->wherePivot('deleted_at', null)->get();
        }
        if ($product_brand_level == 'Product' || $product_brand_level == 'Brief') {
            $product_messages = Product::find($id)->messages()->wherePivot('deleted_at', null)->get();
        }
        $showPresentations = ActualVisitSetting::where('key', 'presentation_actual_visit')->value('value');
        if ($showPresentations == 'Yes') {
            if ($product_brand_level == 'Brand') {
                $product_presentations = ProductBrands::where('brand_id', $id)->first()
                    ->product->presentations()->wherePivot('deleted_at', null)->get();
                $product_presentations = count($product_presentations) > 0 ?
                    $product_presentations : Product::find($id)->presentations()->wherePivot('deleted_at', null)->get();
            }
            if ($product_brand_level == 'Product' || $product_brand_level == 'Brief') {
                $product_presentations = Product::find($id)->presentations()->wherePivot('deleted_at', null)->get();
            }
        }
        return response()->json(compact('product_messages', 'product_presentations'));
    }

    public function getProductName($product, $level)
    {
        if ($level == 'Product')
            return $product->name;
        elseif ($level == 'Brief')
            return $product->short_name;
        else
            return count($product->brands) > 0 ? $product->brands->first()?->name : $product->name;
    }
    public function getProducts(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $line = Line::find($request->line_id);
        $div_id = $request->div_id;
        $id = $div_id != "null" ? [$div_id] : [];
        $setting = Setting::select('value')->where('key', 'product_with_line_or_division')->value('value');
        $products = collect([]);
        if ($setting == 'Division') {
            $products = $user->userDivisionProducts($line, $id)->where('is_hidden', 0)->values();
        } else {
            $products = $user->userProducts($line)->where('is_hidden', 0)->values();
        }
        $product_brand_level = ActualVisitSetting::where('key', 'actual_visit_level')->value('value');

        $products = $products->map(function ($product) use ($product_brand_level) {
            return [
                'id' => $product->id,
                'name' => $this->getProductName($product, $product_brand_level),
            ];
        });

        return $this->respond($products->unique('name')->filter(fn($product) => $product['name'] != null)->values());
    }

    public function getSlides(Presentation $presentation)
    {
        // throw new CrmException($presentation);
        $presentation_slides = PresentationSlide::where('presentation_id', $presentation->id)->get();
        $slides = [];
        foreach ($presentation_slides as  $slide) {
            $path = Attachment::where('id', $slide->slide_id)->value('path');
            array_push($slides, $path);
        }
        return response()->json(['slides' => $slides]);
    }

    public function createPlanActualVisit($id)
    {
        $data = $this->create();
        $plan = PlanVisit::find($id);

        $plan_visit_obj = new DailyViewDataController();
        $line = Line::where('id', $plan->line_id)->first();
        $plan_level = PlanLevel::where(fn($q) => $q->whereNull('line_id')->orWhere('line_id', $line->id))->first()->level;
        $isMultipleDoctors = ActualVisitSetting::where('key', 'multiple_doctors_visit')->value('value') == 'Yes';
        $line_last_level_data = $plan_visit_obj->lineData($line, 1);
        $bricks_data = LineBricks::where('line_division_id', $plan->div_id)
            ->where(fn($q) => $q->where('to_date', '=', null)->orWhere('to_date', '>=', (string)Carbon::now()))
            ->with('brick')->get()->pluck('brick');
        $account_type = Account::where('id', $plan->account_id)->value('type_id');
        $withPharmacyType = AccountType::find($account_type)?->with_pharmacy_type;
        $pharmacyTypes = PharmacyTypeVisit::select('id', 'name')->get();
        $addPharmacyType = ActualVisitSetting::where('key', 'add_pharmacy_type')->value('value') == 'Yes';
        $accounts = $this->getAccounts($plan->line_id, $plan->div_id, 'All', $account_type);
        $doctors = $this->getDoctors($plan->line_id, $plan->account_id, $plan->div_id);

        return response()->json([
            'status' => 'success',
            'plan_level' => $plan_level,
            'isMultipleDoctors' => $isMultipleDoctors,
            'pharmacyTypes' => $pharmacyTypes,
            'addPharmacyType' => $addPharmacyType,
            'withPharmacyType' => $withPharmacyType,
            'divisions' => $line_last_level_data->getData()->divisions,
            'line_division_name' => 'Division',
            'specialities' => $line_last_level_data->getData()->specialities,
            'giveaways' => $line_last_level_data->getData()->giveaways,
            'products' => $line_last_level_data->getData()->products,
            'count_divisions' => count($line_last_level_data->getData()->divisions),
            'min_line_actual_visit_date' => $line_last_level_data->getData()->min_line_actual_visit_date,
            'max_line_actual_visit_date' => $line_last_level_data->getData()->max_line_actual_visit_date,
            'product_brand_level_label' => $line_last_level_data->getData()->product_brand_level_label,
            'bricks' => $bricks_data,
            'lines' => $data->getData()->lines,
            'accountTypes' => $data->getData()->accountTypes,
            'visitTypes' => $data->getData()->visitTypes,
            'double_visit_types' => $data->getData()->double_visit_types,
            'dateDisabled' => $data->getData()->dateDisabled,
            'visitFeedbacks' => $data->getData()->visitFeedbacks,
            'max_products' => $data->getData()->max_products,
            'inputs' => $data->getData()->inputs,
            'accounts' => $accounts->getData()->accounts,
            'doctors' => $doctors->getData()->doctors,
            'plan' => $plan,
            'plan_visit_date' => new Carbon(Carbon::parse($plan->visit_date)->toDateString() . ' ' . Carbon::now()->format('H:i:s')),
            'account_type_id' => $account_type
        ]);
    }
    public function getDoubleVisitTypes($id)
    {
        /**@var User */
        $user = Auth::user();
        $line = $user->hasRole('admin') || $user->hasRole('sub admin') || $user->hasRole('Gemstone Admin') ? Line::select('id', 'name')->first() : $user->lines?->first();
        $is_last_level = $user->divisionType($line)?->last_level == 1;
        $double_visit_types = collect([]);
        if (!$is_last_level) {
            $double_visit_types = DoubleVisitType::where('visit_type_id', $id)->get();
        }
        return $this->respond($double_visit_types);
    }
    public function create()
    {
        /**@var  User $user*/
        $user = Auth::user();
        $is_manager = $user->is_manager;
        $visitTypes = VisitType::select('id', 'name')->get();
        $dateDisabled = ActualVisitSetting::where('key', 'actual_date_disabled')->value('value') == 'Yes';
        $double_visit_types = DoubleVisitType::select('id', 'name')->get();

        $lines = $user->userLines();

        $accountTypes = AccountType::orderBy('sort', 'ASC')->select('account_types.id', 'account_types.name')->get();
        $visitFeedbacks = VisitFeedbacks::orderBy('sort', 'ASC')->select('visit_feedbacks.id', 'visit_feedbacks.notes as name')->whereNull('deleted_at')->get();
        $max_products = ActualVisitSetting::where('key', 'max_products')->first();
        $inputs = AvRequiredInput::where('select', 1)->get();
        return response()->json([
            'status' => 'success',
            'lines' => $lines,
            'is_manager' => $is_manager,
            'accountTypes' => $accountTypes,
            'visitTypes' => $visitTypes,
            'double_visit_types' => $double_visit_types,
            'dateDisabled' => $dateDisabled,
            'visitFeedbacks' => $visitFeedbacks,
            'max_products' => $max_products->value,
            'inputs' => $inputs,
        ]);
    }

    public function getMapMessage(Request $request)
    {
        $location_setting = Setting::where('key', 'location_gmaps')->first()->value;
        $position = $request->get('position');
        // throw new CrmException($position['lat']);
        $data = [];
        if ($location_setting == 'yes') {
            if ($position['lat'] != 0 && $position['lng'] != 0) {
                $data['message'] = "Your Location have got successfully with lat = " . $position['lat'] . " and lng = " . $position['lng'];
                $data['color'] = "blue";
            } else {
                $data['message'] = "Your Location have failed to save please refresh your page.";
                $data['color'] = "red";
            }
        }
        return $this->respond(compact('data', 'location_setting'));
    }


    private function SaveVisitPerDoctor($request, $copy_actual_visit_setting, $isMultipleDoctors, $location_setting, $hasApprovable, $user)
    {
        $perAccount = 0;
        if ($location_setting == 'yes') {
            if ($request->ll != 0 && $request->lg != 0) {
                $actual_visit = $this->saveActual($request, $copy_actual_visit_setting, $isMultipleDoctors, null, $perAccount);
                // saving visit ll and lg in account lines
                $location = AccountLines::where('line_id', $request->line_id)
                    ->where('account_id', $request->account_id)
                    ->where('line_division_id', $request->div_id)->first();
                if ($location->ll == null && $location->lg == null) {
                    $location = $location->update([
                        'll' => $request->ll,
                        'lg' => $request->lg,
                        'visit_id' => $actual_visit->id,
                    ]);
                }
            } else {
                throw new Exception('Please Check Your Location');
            }
        } else {
            $actual_visit = $this->saveActual($request, $copy_actual_visit_setting, $isMultipleDoctors, null, $perAccount);
        }
        if (!empty($request->visitedDoctors)) {
            foreach ($request->visitedDoctors as $doctor) {
                ActualVisitedDoctor::create([
                    'visit_id' => $actual_visit->id,
                    'doctor' => $doctor
                ]);
            }
        }
        PlanVisitDetails::firstOrCreate([
            'visitable_id' => $actual_visit->id,
            'visitable_type' => ActualVisit::class,
            'approval' => $hasApprovable ? null : 1
        ]);
        if ($actual_visit->visit_type_id == 1) {
            $managerIds = $user->approvableUsers($request->line_id)?->pluck('id')->values() ?? [];
            $doublePlan = PlanVisit::where('account_id', $request->account_id)
                ->where('account_dr_id', $request->account_dr_id)
                ->where('visit_type', 2)
                ->whereIntegerInRaw('user_id', $managerIds)
                ->whereDate('visit_date', Carbon::parse($request->visit_date)->toDateString())
                ->whereHas('details', function ($q) {
                    $q->where('approval', 1);
                })->first();
            if ($doublePlan) {
                NotificationHelper::send(
                    collect(User::where('id', $doublePlan->user_id)->get()),
                    new DoublePlanNotification($user->fullname . ' convert plan with single type and you took it as double plan', auth()->user())
                );
            }
        }
        $model_id = $actual_visit->id;
        $model_type = ActualVisit::class;
        LogActivity::addLog($model_id, $model_type);
    }

    private function saveVisitPerAccount($request, $copy_actual_visit_setting, $isMultipleDoctors, $location_setting, $hasApprovable, $user)
    {
        $perAccount = 1;
        foreach ($request->account_dr_ids as $doctor_id) {
            if ($location_setting == 'yes') {
                if ($request->ll != 0 && $request->lg != 0) {
                    $actual_visit = $this->saveActual($request, $copy_actual_visit_setting, $isMultipleDoctors, $doctor_id, $perAccount);
                    // saving visit ll and lg in account lines
                    $location = AccountLines::where('line_id', $request->line_id)
                        ->where('account_id', $request->account_id)
                        ->where('line_division_id', $request->div_id)->first();
                    if ($location->ll == null && $location->lg == null) {
                        $location = $location->update([
                            'll' => $request->ll,
                            'lg' => $request->lg,
                            'visit_id' => $actual_visit->id,
                        ]);
                    }
                } else {
                    throw new Exception('Please Check Your Location');
                }
            } else {
                $actual_visit = $this->saveActual($request, $copy_actual_visit_setting, $isMultipleDoctors, $doctor_id, $perAccount);
            }
            if (!empty($request->visitedDoctors)) {
                foreach ($request->visitedDoctors as $doctor) {
                    ActualVisitedDoctor::create([
                        'visit_id' => $actual_visit->id,
                        'doctor' => $doctor
                    ]);
                }
            }
            PlanVisitDetails::firstOrCreate([
                'visitable_id' => $actual_visit->id,
                'visitable_type' => ActualVisit::class,
                'approval' => $hasApprovable ? null : 1
            ]);
            $model_id = $actual_visit->id;
            $model_type = ActualVisit::class;
            LogActivity::addLog($model_id, $model_type);
        }
    }



    public function storePerAccount(ActualVisitPerAccountRequest $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $hasApprovable = '';
        $hasApprovable = $user->division(Line::find($request->line_id))?->divisionType?->planable?->where('line_id', $request->line_id)->first()?->approvables()?->wherePivot('request_type', ActualVisit::class)->count() > 0;

        $location_setting = Setting::where('key', 'location_gmaps')->value('value');
        $copy_actual_visit_setting = ActualVisitSetting::where('key', 'copy_actual_visit')->first();
        $isMultipleDoctors = ActualVisitSetting::where('key', 'multiple_doctors_visit')->value('value') == 'Yes';
        DB::transaction(function () use (
            $request,
            $user,
            $copy_actual_visit_setting,
            $location_setting,
            $isMultipleDoctors,
            $hasApprovable
        ) {
            $this->saveVisitPerAccount($request, $copy_actual_visit_setting, $isMultipleDoctors, $location_setting, $hasApprovable, $user);
        });

        return response()->json([
            'status' => 'success',
            'message' => 'Actual Visit Created Successfully'
        ]);
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(ActualVisitRequest $request)
    {
        // throw new CrmException($request->all());
        /**@var User $user */
        $user = Auth::user();
        $hasApprovable = '';
        $hasApprovable = $user->division(Line::find($request->line_id))?->divisionType?->planable?->where('line_id', $request->line_id)->first()?->approvables()?->wherePivot('request_type', ActualVisit::class)->count() > 0;

        $location_setting = Setting::where('key', 'location_gmaps')->value('value');
        $copy_actual_visit_setting = ActualVisitSetting::where('key', 'copy_actual_visit')->first();
        $isMultipleDoctors = ActualVisitSetting::where('key', 'multiple_doctors_visit')->value('value') == 'Yes';
        DB::transaction(function () use (
            $request,
            $user,
            $copy_actual_visit_setting,
            $location_setting,
            $isMultipleDoctors,
            $hasApprovable
        ) {
            $this->SaveVisitPerDoctor($request, $copy_actual_visit_setting, $isMultipleDoctors, $location_setting, $hasApprovable, $user);
        });

        return response()->json([
            'status' => 'success',
            'message' => 'Actual Visit Created Successfully'
        ]);
    }
    // }

    private function saveActual($request, $copy_actual_visit_setting, $isMultipleDoctors, $doctor_id = null, $perAccount)
    {
        $user = Auth::user();
        if ($request->plan_id == null) {
            $plan = PlanVisit::where('account_id', $request->account_id)
                ->where('account_dr_id', $request->account_dr_id)
                ->whereDate('visit_date', Carbon::parse($request->visit_date)->toDateString())
                ->where('user_id', $user->id)
                ->whereHas('details', function ($q) {
                    $q->where('approval', 1);
                })->first();
            if ($plan) $request->plan_id = $plan->id;
        }
        $speciality_id = Doctor::find($doctor_id ?? $request->account_dr_id)->speciality_id;
        $actual_visit = ActualVisit::create([
            'user_id' => $user->id,
            'plan_id' => $request->plan_id,
            'line_id' => $request->line_id,
            'div_id' => $request->div_id,
            'brick_id' => $request->brick_id != 'All'
                ? $request->brick_id
                : AccountLines::where('line_id', $request->line_id)
                ->where('line_division_id', $request->div_id)
                ->where('account_id', $request->account_id)->first()->brick_id,
            'acc_type_id' => $request->account_type_id,
            'shift_id' => $request->shift_id,
            'pharmacy_type_id' => $request->pharmacy_type_id,
            'speciality_id' => $speciality_id,
            'account_id' => $request->account_id,
            'account_dr_id' => $doctor_id ?? $request->account_dr_id,
            'visit_type_id' => $request->visit_type_id,
            'double_visit_type_id' => $request->double_visit_type_id,
            'visit_date' => new Carbon($request->visit_date),
            'end_visit_date' => Carbon::now()->toDateTimeString(),
            // 'visit_duration' => $duration,
            "invalid_duration" => 1,
            'll' => $request->ll,
            'lg' => $request->lg,
        ]);

        // Saving Givaways
        if ($request->giveaways) {
            $giveawayData = array_map(fn($giveaway) => [
                'visit_id' => $actual_visit->id,
                'giveaway_id' => $giveaway['giveaway_id'],
                'units' => $giveaway['units']
            ], $request->giveaways);
            VisitGiveaway::insert($giveawayData);
        }
        // Saving Products
        if ($request->products) {
            $productData = collect([]);
            // saving Orders
            $orderRequest = OrderRequest::create([
                'visit_id' => $actual_visit->id,
                'user_id' => $actual_visit->user_id
            ]);
            if ($perAccount && $isMultipleDoctors) {
                $allowedProductIds = ProductSpecialities::where('speciality_id', $speciality_id)
                    ->where('from_date', '<=', now())
                    ->where(fn($q) => $q->where('to_date', '>', now())
                        ->orWhere('to_date', null))
                    ->pluck('product_id')
                    ->toArray();
                $productData = collect($request->products)
                    ->filter(function ($product) use ($allowedProductIds) {
                        return in_array($product['product_id'], $allowedProductIds);
                    })->values();
                if (count($productData) == 0) {
                    $productData = collect($request->products);
                }
                // throw new CrmException($allowedProductIds);
            } else {
                $productData = collect($request->products);
            }
            $productData = $productData->map(function ($product) use ($actual_visit, $request) {
                return [
                    'visit_id' => $actual_visit->id,
                    'product_id' => $product['product_id'],
                    'brand_id' => $request->level === "Brand" ?
                        Product::find($product['product_id'])->brands->first()->id ?? null : null,
                    'message_id' => isNullable($product['message_id']) ? null : $product['message_id'],
                    'samples' => $product['samples'],
                    'current_order' => $product['order'] ?? 0,
                    'stock' => $product['stock'] ?? 0,
                    'notes' => $product['notes'] ?? null,
                    'follow_up' => $product['follow_up'] ?? null,
                    'market_feedback' => $product['market_feedback'] ?? null,
                    'vfeedback_id' => $product['vFeedback_id']
                ];
            })->values()->toArray();

            VisitProduct::insert($productData);

            $orderData = collect($request->products)->map(function ($product) use ($orderRequest, $request) {
                return [
                    'order_request_id' => $orderRequest?->id,
                    'product_id' => $product['product_id'],
                    'quantity' => $product['order'] ?? 0
                ];
            })->toArray();
            OrderRequestDetail::insert($orderData);
        }

        // Saving EDetailing
        if ($request->slides) {
            Statistic::whereIn('id', $request->slides)->update([
                'visit_id' => $actual_visit->id
            ]);
        }
        if ($request->attachments) {
            $attachments = [];
            foreach ($request->attachments as $attachment) {
                $attachments[] = [
                    'attachable_id' => $actual_visit->id,
                    'attachable_type' => ActualVisit::class,
                    'path' => $attachment,
                ];
            }
            Attachment::insert($attachments);
        }

        if ($copy_actual_visit_setting->value == 'Yes') {
            if ($request->visit_type_id == 2 && $request->managers != null) {
                $managers_list = $request->managers;
                foreach ($managers_list as $manager) {
                    ActualVisitManager::create([
                        'visit_id' => $actual_visit->id,
                        'user_id' => $manager,
                    ]);

                    DB::transaction(function () use ($request, $manager, $doctor_id, $perAccount, $speciality_id, $isMultipleDoctors) {
                        $managerPlan = PlanVisit::where('account_id', $request->account_id)
                            ->where('account_dr_id', $doctor_id ?? $request->account_dr_id)
                            ->whereDate('visit_date', Carbon::parse($request->visit_date)->toDateString())
                            ->where('user_id', $manager)
                            ->whereHas('details', function ($q) {
                                $q->where('approval', 1);
                            })->first();

                        $manager_actual_visit = ActualVisit::create([
                            'user_id' => $manager,
                            'plan_id' => $managerPlan?->id ?? $request->plan_id,
                            'line_id' => $request->line_id,
                            'div_id' => $request->div_id,
                            'brick_id' => $request->brick_id != 'All'
                                ? $request->brick_id
                                : AccountLines::where('line_id', $request->line_id)
                                ->where('line_division_id', $request->div_id)
                                ->where('account_id', $request->account_id)->first()->brick_id,
                            'acc_type_id' => $request->account_type_id,
                            'speciality_id' => $speciality_id,
                            'shift_id' => $request->shift_id,
                            'pharmacy_type_id' => $request->pharmacy_type_id,
                            'account_id' => $request->account_id,
                            'account_dr_id' => $doctor_id ?? $request->account_dr_id,
                            'visit_type_id' => $request->visit_type_id,
                            'double_visit_type_id' => $request->double_visit_type_id,
                            'visit_date' => new Carbon($request->visit_date),
                            'end_visit_date' => Carbon::now()->toDateTimeString(),
                            'll' => $request->ll,
                            'lg' => $request->lg,
                            'is_automatic' => 1,
                        ]);

                        // Saving Givaways
                        if ($request->giveaways) {
                            $giveawayData = array_map(fn($giveaway) => [
                                'visit_id' => $manager_actual_visit->id,
                                'giveaway_id' => $giveaway['giveaway_id'],
                                'units' => $giveaway['units']
                            ], $request->giveaways);
                            VisitGiveaway::insert($giveawayData);
                        }
                        if ($request->products) {
                            if ($perAccount && $isMultipleDoctors) {
                                $allowedProductIds = ProductSpecialities::where('speciality_id', $speciality_id)
                                    ->where('from_date', '<=', now())
                                    ->where(fn($q) => $q->where('to_date', '>', now())
                                        ->orWhere('to_date', null))
                                    ->pluck('product_id')
                                    ->toArray();
                                $productData = collect($request->products)
                                    ->filter(function ($product) use ($allowedProductIds) {
                                        return in_array($product['product_id'], $allowedProductIds);
                                    })->values();
                                if (count($productData) == 0) {
                                    $productData = collect($request->products);
                                }
                            } else {
                                $productData = collect($request->products);
                            }
                            $productData = $productData->map(function ($product) use ($manager_actual_visit, $request) {
                                return [
                                    'visit_id' => $manager_actual_visit->id,
                                    'product_id' => $product['product_id'],
                                    'brand_id' => $request->level === "Brand" ?
                                        Product::find($product['product_id'])->brands->first()->id ?? null : null,
                                    'message_id' => isNullable($product['message_id']) ? null : $product['message_id'],
                                    'samples' => $product['samples'],
                                    'current_order' => $product['order'] ?? 0,
                                    'stock' => $product['stock'] ?? 0,
                                    'notes' => $product['notes'] ?? null,
                                    'follow_up' => $product['follow_up'] ?? null,
                                    'market_feedback' => $product['market_feedback'] ?? null,
                                    'vfeedback_id' => $product['vFeedback_id']
                                ];
                            })->values()->toArray();

                            VisitProduct::insert($productData);
                        }

                        if ($request->attachments) {
                            $attachments = [];
                            foreach ($request->attachments as $attachment) {
                                $attachments[] = [
                                    'attachable_id' => $manager_actual_visit->id,
                                    'attachable_type' => ActualVisit::class,
                                    'path' => $attachment,
                                ];
                            }
                            Attachment::insert($attachments);
                        }
                    });
                }
            }
        } else {
            if ($request->visit_type_id == 2 && $request->managers != null) {
                $managers_list = $request->managers;
                foreach ($managers_list as $manager) {
                    ActualVisitManager::create([
                        'visit_id' => $actual_visit->id,
                        'user_id' => $manager,
                    ]);
                }
            }
        }
        return $actual_visit;
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $visit_types = VisitType::get();
        $actual_visit = ActualVisit::select(
            'actual_visits.id AS id',
            'actual_visits.plan_id AS plan_id',
            'actual_visits.div_id',
            'actual_visits.user_id',
            'u.id AS user_id',
            'u.fullname AS User',
            'm.id AS manager_id',
            'm.fullname AS Manager',
            'lines.name AS Line',
            'lines.id AS line_id',
            'line_divisions.name AS Line Division',
            'accounts.name AS Account',
            'shifts.name AS Shift',
            'pharmacy_type_visits.name AS Pharmacy Type',
            'specialities.name AS Speciality',
            'bricks.name AS Brick',
            'accounts.id AS account_id',
            'doctors.name AS Doctor',
            'doctors.id AS doctor_id',
            'visit_types.name AS Visit Type',
            'visit_types.id AS Visit Type id',
            'double_visit_types.name AS Double Type',
            'actual_visits.visit_date AS actual_visit_date',
            'actual_visits.ll',
            'actual_visits.lg',
        )
            ->leftJoin('users as u',  'actual_visits.user_id', 'u.id')
            ->leftJoin('actual_visits_managers', 'actual_visits_managers.visit_id', 'actual_visits.id')
            ->leftJoin('shifts', 'shifts.id', 'actual_visits.shift_id')
            ->leftJoin('bricks', 'bricks.id', 'actual_visits.brick_id')
            ->leftJoin('pharmacy_type_visits', 'pharmacy_type_visits.id', 'actual_visits.pharmacy_type_id')
            ->leftJoin('users as m', 'actual_visits_managers.user_id', 'm.id')
            ->leftJoin('lines', 'lines.id', 'actual_visits.line_id')
            ->leftJoin('line_divisions', 'line_divisions.id', 'actual_visits.div_id')
            ->leftJoin('accounts', 'accounts.id', 'actual_visits.account_id')
            ->leftJoin('doctors', 'doctors.id', 'actual_visits.account_dr_id')
            ->leftJoin('specialities', 'specialities.id', 'actual_visits.speciality_id')
            ->leftJoin('visit_types', 'actual_visits.visit_type_id', 'visit_types.id')
            ->leftJoin('double_visit_types', 'actual_visits.double_visit_type_id', 'double_visit_types.id')
            ->where('actual_visits.id', $id)->with('actualVisitManagers')->first();
        $product_brand_level = ActualVisitSetting::where('key', 'actual_visit_level')->value('value');
        $visit_products = VisitProduct::where('visit_id', $id);
        $all_visit_products = VisitProduct::where('visit_id', $id);
        $visitFeedbacks = VisitFeedbacks::select('id', 'notes')->get();
        $visit_products = $visit_products->with('product')->get()->map(function ($visit_product) use ($id) {
            return [
                'id' => $visit_product->product->id,
                'name' => $visit_product->product?->name,
                'brand' => $visit_product->product?->brands?->first()?->name ?? '',
                'brief' => $visit_product->product->short_name ?? '',
                'message' => $visit_product->message->message ?? '',
                'comment' => $visit_product?->notes ?? '',
                'market_feedback' => $visit_product->market_feedback ?? '',
                'follow_up' => $visit_product->follow_up ?? '',
                'samples' => $visit_product->samples ?? '',
                'order' => $visit_product->current_order ?? '',
                'stock' => $visit_product->stock ?? '',
                'ucode' => $visit_product->product->ucode ?? '',
                'quantity' => $visit_product->product->quantity ?? '',
                'launch_date' => Carbon::parse($visit_product->product->launch_date)->toDateString() ?? '',
            ];
        });

        $old_visit_products =  $all_visit_products->with('feedback', 'product')->get()->map(function ($visit_product) use ($id) {
            return [
                'product_id' => $visit_product->product->id,
                'feedback_id' => $visit_product->vfeedback_id,
                'brand_id' => $visit_product->brand_id,
                'samples' => $visit_product->samples,
                'follow_up' => $visit_product->follow_up,
                'market_feedback' => $visit_product->market_feedback,
                'message_id' => $visit_product->message_id,
                'current_order' => $visit_product->current_order,
                'stock' => $visit_product->stock,
                'payment' => $visit_product->payment,
                'notes' => $visit_product?->notes,
            ];
        });

        $visitUser = User::find($actual_visit->user_id);
        $line = Line::find($actual_visit->line_id);
        $div_ids = [$actual_visit->div_id];
        $managers = ActualVisitManager::where('visit_id', $id)->get()->pluck('user_id');
        $visit_products_notes = VisitProduct::where('visit_id', $id)->first()?->notes;
        $visit_products_feedback = VisitProduct::where('visit_id', $id)->first()?->market_feedback;
        $setting = Setting::select('value')->where('key', 'product_with_line_or_division')->value('value');
        $products = collect([]);
        if ($setting == 'Division') {
            $products = $visitUser->userDivisionProducts($line, $div_ids);
        } else {
            $products = $visitUser->userProducts($line);
        }
        $products = $products->map(function ($product) use ($product_brand_level) {
            return [
                'id' => $product->id,
                'name' => $this->getProductName($product, $product_brand_level),
            ];
        });
        $visit_products_ids = $visit_products->pluck('id');
        $visit_giveaways = VisitGiveaway::where('visit_id', $id)->with('giveaway')->get()->map(function ($giveaway) {
            return [
                'id' => $giveaway->id,
                'name' => $giveaway->giveaway->name,
                'units' => $giveaway->units,
            ];
        });

        $location = [
            "id" => $actual_visit['id'],
            "position" => [
                'visit_id' => $actual_visit['id'],
                'account' => Account::find($actual_visit['account_id'])->name,
                'doctor' => $actual_visit['doctor_id'] ? Doctor::find($actual_visit['doctor_id'])->name : '',
                'lat' => (float)$actual_visit['ll'],
                'lng' => (float)$actual_visit['lg'],
            ],
            "positionAccount" => [
                'account_id' => $actual_visit['account_id'],
                'account' => Account::find($actual_visit['account_id'])->name,
                'doctor' => $actual_visit['doctor_id'] ? Doctor::find($actual_visit['doctor_id'])->name : '',
                'lat' => (float)Account::find($actual_visit['account_id'])?->accountlines()->where('line_id', $actual_visit['line_id'])->first()->ll,
                'lng' => (float)Account::find($actual_visit['account_id'])?->accountlines()->where('line_id', $actual_visit['line_id'])->first()->lg,
            ],
        ];

        $attachments = Attachment::where('attachable_id', $id)->where('attachable_type', ActualVisit::class)->get()
            ->map(function ($visit) {
                return [
                    'id' => $visit->attachable_id,
                    'file' => $visit->path,
                ];
            });
        $user = Auth::user();
        $feedbacks = ActualDoubleFeedback::where('user_id', $user->id)->where('visit_id', $actual_visit->id)
            ->get()->map(function ($feedback) {
                return [
                    'id' => $feedback->id,
                    'user' => $feedback->user?->fullname,
                    'feedback' => $feedback->feedback
                ];
            });
        $model_id = $id;
        $model_type = ActualVisit::class;

        LogActivity::addLog($model_id, $model_type);
        return response()->json(
            [
                'visit_types' => $visit_types,
                'actual_visit' => $actual_visit,
                'products' => $products->values(),
                'visit_products_notes' => $visit_products_notes,
                'visit_products_feedback' => $visit_products_feedback,
                'visit_products_ids' => $visit_products_ids,
                'visit_products' => $visit_products,
                'visit_giveaways' => $visit_giveaways,
                'managers' => $managers,
                'old_visit_products' => $old_visit_products,
                'location' => $location,
                'attachments' => $attachments,
                'feedbacks' => $feedbacks,
                'visitFeedbacks' => $visitFeedbacks,
            ]
        );
    }
    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $visit = ActualVisit::find($id);
        $copy_actual_visit_setting = ActualVisitSetting::where('key', 'copy_actual_visit')->first();
        // throw new CrmException($request->all());
        DB::transaction(function () use ($visit, $id, $request, $copy_actual_visit_setting) {
            $visit->visit_type_id = $request->visit_type_id ? $request->visit_type_id : $visit->visit_type_id;
            $visit->visit_date = Carbon::parse($request->date)->toDateTimeString();
            $visit->created_at = Carbon::parse($request->date)->toDateTimeString();
            $visit->save();
            if ($request->visit_type_id) {
                if ($request->visit_type_id == 1) {
                    ActualVisitManager::where('visit_id', $id)->forceDelete();
                }
                if ($request->visit_type_id == 2) {
                    if ($copy_actual_visit_setting->value == 'Yes') {
                        ActualVisitManager::where('visit_id', $id)->forceDelete();
                        for ($i = 0; $i < count($request->managers_ids); $i++) {
                            $manager = ActualVisitManager::create([
                                'user_id' => $request->managers_ids[$i],
                                'visit_id' => $id,
                            ]);
                            $manager_visit = ActualVisit::create([
                                'user_id' => $manager->user_id,
                                'plan_id' => $visit->plan_id,
                                'line_id' => $visit->line_id,
                                'div_id' => $visit->div_id,
                                'brick_id' => $visit->brick_id,
                                'acc_type_id' => $visit->acc_type_id,
                                'speciality_id' => $visit->speciality_id,
                                'account_id' => $visit->account_id,
                                'account_dr_id' => $visit->account_dr_id,
                                'visit_type_id' => $visit->visit_type_id,
                                'visit_date' => new Carbon($visit->visit_date),
                                'll' => $visit->ll,
                                'lg' => $visit->lg,
                            ]);

                            if (count($visit->actualVisitGiveaways) > 0) {
                                foreach ($visit->actualVisitGiveaways as $giveaway) {
                                    VisitGiveaway::create([
                                        'visit_id' => $id,
                                        'giveaway_id' => $giveaway['giveaway_id'],
                                        'units' => $giveaway['units']
                                    ]);
                                }
                            }

                            if (count($visit->actualVisitProducts) > 0) {
                                foreach ($visit->actualVisitProducts as $product) {
                                    // throw new CrmException($product);
                                    VisitProduct::create([
                                        'visit_id' => $manager_visit->id,
                                        'product_id' => $product->product_id,
                                        'brand_id' => $product->brand_id,
                                        'message_id' => $product->message_id,
                                        'samples' => $product->samples,
                                        'notes' => $product->notes,
                                        'follow_up' => $product->follow_up,
                                        'follow_up_flag' => $product->follow_up_flag,
                                        'market_feedback' => $product->market_feedback,
                                        'vfeedback_id' => $product->vfeedback_id
                                    ]);
                                }
                            }
                        }
                    } else {
                        ActualVisitManager::where('visit_id', $id)->forceDelete();
                        for ($i = 0; $i < count($request->managers_ids); $i++) {
                            $manager = ActualVisitManager::create([
                                'user_id' => $request->managers_ids[$i],
                                'visit_id' => $id,
                            ]);
                        }
                    }
                }
            }

            if (!empty($request->products)) {
                $visit->actualVisitProducts()->forceDelete();
                foreach ($request->products as $product) {
                    VisitProduct::create([
                        'visit_id' => $id,
                        'product_id' => $product['product_id'],
                        'notes' => $product['notes'],
                        'samples' => $product['samples'],
                        'payment' => $product['payment'],
                        'follow_up' => $product['follow_up'],
                        'market_feedback' => $product['market_feedback'],
                        'current_order' => $product['current_order'],
                        'vfeedback_id' => $product['feedback_id'],
                    ]);
                }
            }
        });


        $model_id = $id;
        $model_type = ActualVisit::class;
        LogActivity::addLog($model_id, $model_type);
        return response()->json(['status' => 'success']);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $model_id = $id;
        $model_type = ActualVisit::class;
        $visit = ActualVisit::find($id);
        $actual_details = $visit->details;
        $actual_product = VisitProduct::where('visit_id', $id)->exists();
        $actual_giveaway = VisitGiveaway::where('visit_id', $id)->exists();
        $actual_manager = ActualVisitManager::where('visit_id', $id)->exists();
        $actual_visited_doctors = ActualVisitedDoctor::where('visit_id', $id)->exists();
        $feedback_double_visit = ActualDoubleFeedback::where('user_id', Auth::id())->where('visit_id', $id)->exists();
        $detailing = Statistic::where('visit_id', $id)->exists();

        // throw new CrmException($account_lines);
        if ($actual_details)
            $visit->details()->delete();
        if ($actual_product)
            $visit->actualVisitProducts()->forceDelete();
        if ($actual_manager)
            $visit->actualVisitManagers()->forceDelete();
        if ($actual_giveaway)
            $visit->actualVisitGiveaways()->forceDelete();
        if ($actual_visited_doctors)
            $visit->actualVisitedDoctors()->forceDelete();
        if ($visit->attachments)
            $visit->attachments()->forceDelete();
        if ($detailing)
            $visit->detailing()->forceDelete();
        if ($feedback_double_visit)
            $visit->actualVisitFeedbacks()->where('user_id', Auth::id())->forceDelete();


        AccountLines::where('visit_id', $id)->update([
            'visit_id' => null
        ]);
        $visit->forceDelete();
        LogActivity::addLog($model_id, $model_type);
        return response()->json(['status' => 'success']);
    }

    public function getAccountsRequest(Request $request)
    {
        if (!$request->div_id || !$request->brick_id || !$request->line_id) {
            throw new Exception('Please check line , division and brick');
        }
        return $this->getAccounts($request->line_id, $request->div_id, $request->brick_id, $request->account_type_id);
    }
    public function getAccounts($line_id, $div_id, $brick_id, $account_type_id)
    {
        $setting = ListType::first()->type == 'Default List' ? true : false;
        $accounts = Account::select('accounts.id', 'accounts.name', 'account_lines.ll as ll', 'account_lines.lg as lg', 'accounts.type_id', 'accounts.active_date')
            ->where('accounts.active_date', '<=', Carbon::now())
            ->where(fn($q) => $q->where('accounts.inactive_date', '=', null)
                ->orWhere('accounts.inactive_date', '>=', (string)Carbon::now()));
        $isAmShift = AccountType::find($account_type_id)->shift->id == 1;
        if (!$setting) {
            $accounts = $accounts
                ->join('account_lines', function ($join) use ($line_id, $div_id) {
                    $join->on('accounts.id', 'account_lines.account_id')
                        ->where('account_lines.line_id', $line_id)
                        ->where('account_lines.line_division_id', $div_id)
                        ->where('account_lines.deleted_at', null)
                        ->where('account_lines.from_date', '<=', Carbon::now())
                        ->where(fn($q) => $q->where('account_lines.to_date', '=', null)
                            ->orWhere('account_lines.to_date', '>=', (string)Carbon::now()));
                })
                ->join(
                    'new_account_doctors',
                    function ($join) use ($line_id) {
                        $join->on('accounts.id', '=', 'new_account_doctors.account_id');
                        $join->on('account_lines.id', '=', 'new_account_doctors.account_lines_id')
                            ->where('new_account_doctors.from_date', '<=', Carbon::now())
                            ->where('new_account_doctors.line_id', $line_id)
                            ->where(fn($q) => $q->where('new_account_doctors.to_date', '=', null)
                                ->orWhere('new_account_doctors.to_date', '>=', (string)Carbon::now()));;
                    }
                );
            if ($brick_id != "All") {
                $accounts = $accounts->where('account_lines.brick_id', $brick_id);
            }
        } else {
            $accounts = $accounts->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
                ->whereHas('accountlines', function ($q) use ($line_id, $div_id, $brick_id) {
                    $q->where('account_lines.line_id', $line_id)
                        ->where('account_lines.line_division_id', $div_id)
                        ->where('account_lines.deleted_at', null)
                        ->where('account_lines.from_date', '<=', Carbon::now())
                        ->where(fn($q) => $q->where('account_lines.to_date', '=', null)
                            ->orWhere('account_lines.to_date', '>=', (string)Carbon::now()));
                    if ($brick_id != "All") {
                        $q = $q->where('account_lines.brick_id', $brick_id);
                    }
                })->whereHas('newaccountdoctors', function ($q) use ($line_id) {
                    $q->where('new_account_doctors.deleted_at', null)
                        ->where('new_account_doctors.from_date', '<=', Carbon::now())
                        ->where('new_account_doctors.line_id', $line_id)
                        ->where(fn($q) => $q->where('new_account_doctors.to_date', '=', null)
                            ->orWhere('new_account_doctors.to_date', '>=', (string)Carbon::now()));
                });
        }

        if ($account_type_id != 0) {
            $accounts = $accounts->where('type_id', $account_type_id);
        }
        $accounts = $accounts->get()->unique('id')->values()->toArray();
        usort($accounts, function ($a, $b) {
            return strcasecmp($a['name'], $b['name']);
        });
        $withPharmacyType = AccountType::find($account_type_id)?->with_pharmacy_type;
        return response()->json([
            'status' => 'success',
            'accounts' => $accounts,
            'isAmShift' => $isAmShift,
            'withPharmacyType' => $withPharmacyType,
        ]);
    }
    public function accountLocation(Request $request)
    {
        $account = Account::find($request->id);
        $accountLine = AccountLines::select('ll', 'lg')->where('account_id', $account->id)->where('line_id', $request->line_id)
            ->where('line_division_id', $request->div_id)
            ->where('from_date', '<=', (string)Carbon::now())
            ->where(fn($q) => $q->where('to_date', '>=', (string)Carbon::now())
                ->orWhere('to_date', null))->first();
        // throw new CrmException($account);
        $location = [
            'account_id' => $account->id,
            'account' => $account->name,
            'doctor' => $account->name,
            'lat' => (float)$accountLine->ll,
            'lng' => (float)$accountLine->lg
        ];
        return $this->respond($location);
    }
    public function getDoctors($line, $account_id, $div_id)
    {
        $final_doctors = array();
        $specialityIds = Line::find($line)->specialities->pluck('id');
        $doctors = NewAccountDoctor::select('new_account_doctors.id', 'doctor_id')
            // ->leftJoin('account_lines', 'new_account_doctors.account_lines_id', 'account_lines.id')
            ->join('doctors', function ($join) use ($specialityIds) {
                $join->on('doctors.id', 'new_account_doctors.doctor_id')
                    ->whereIntegerInRaw('doctors.speciality_id', $specialityIds);
            })
            ->whereNull('doctors.deleted_at')
            ->where('new_account_doctors.account_id', $account_id)
            ->where('new_account_doctors.line_id', $line)
            // ->where('account_lines.line_division_id', $div_id)
            ->where('new_account_doctors.from_date', '<=', Carbon::now())
            ->where(fn($q) => $q->where('new_account_doctors.to_date', '=', null)->orWhere('new_account_doctors.to_date', '>=', (string)Carbon::now()))
            ->get()->unique('doctor_id')->values();

        foreach ($doctors as $doctor) {
            $final_doctors[] = array(
                'id' => $doctor->doctor->id,
                'ref' => $doctor->id,
                'name' => $doctor->doctor->name
            );
        }

        usort($final_doctors, function ($a, $b) {
            return strcasecmp($a['name'], $b['name']);
        });

        // dd($final_doctors);

        return response()->json([
            'status' => 'success',
            'doctors' => $final_doctors
        ]);
    }

    public function importVisits(ImportRequest $request)
    {
        ActualVisit::import($request);
        return $this->respondSuccess();
    }
    public function exportActuals()
    {
        return ActualVisit::export(ExcelType::XLSX);
    }
    public function exportActualsCsv()
    {
        return ActualVisit::export(ExcelType::CSV);
    }

    public function exportActualspdf()
    {
        $actualvisits = ActualVisit::where('deleted_at', null);
        /**@var User $user */
        $user = Auth::user();
        $allUsers = $user->allBelowUsers()->pluck('id')->push(auth()->id());
        if (!$user->hasRole('admin'))
            $actualvisits = $actualvisits->whereIn('user_id', $allUsers);
        $actualvisits = $actualvisits->get();
        return ActualVisit::exportPdf($actualvisits);
    }

    public function sendActualsmail(MailRequest $request)
    {
        $actualvisits = ActualVisit::where('deleted_at', null)->get();
        return ActualVisit::sendMail($request, $actualvisits);
    }


    public function policies()
    {
        $policies = Policy::where('policiable_type', ActualVisit::class)->get();
        return $this->respond(['policies' => $policies]);
    }
    public function saveDoubleLocation(Request $request)
    {
        if ($request->ll == '0.0' && $request->lg == '0.0') {
            throw new Exception('Please refresh and press another time');
        }
        DoubleVisitLocation::firstOrCreate([
            'visit_id' => $request->id,
            'user_id' => Auth::user()->id
        ], [
            'll' => $request->ll,
            'lg' => $request->lg,
        ]);
        return $this->respondSuccess();
    }
}
