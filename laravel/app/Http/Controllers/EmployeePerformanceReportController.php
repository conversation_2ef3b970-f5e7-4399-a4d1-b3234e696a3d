<?php

namespace App\Http\Controllers;

use App\AccountType;
use App\CallRate;
use App\Classes;
use App\DivisionType;
use App\Doctor;
use App\Exceptions\CrmException;
use App\Http\Controllers\Controller;
use App\Line;
use App\LineDivision;
use App\Models\CommercialRequest\CommercialRequest;
use App\Models\Kpi;
use App\Models\OffDay;
use App\OwActualVisit;
use App\Services\ActualService;
use App\Services\DoctorService;
use App\Services\Enums\KPITypes;
use App\Services\PlanService;
use App\Services\PostVisitKpisService;
use App\Setting;
use App\Shift;
use App\Speciality;
use App\User;
use App\Vacation;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class EmployeePerformanceReportController extends ApiController
{
    public function __construct(private readonly PostVisitKpisService $postVisitKpisService) {}
    public function filter(Request $request)
    {
        /**@var User authUser */
        $authUser = Auth::user();
        $visit = $request->kpisFilter;
        // throw new CrmException($visit);
        $from = Carbon::parse($visit['fromDate'])->startOfDay();
        $to = Carbon::parse($visit['toDate'])->endOfDay();
        $month = [Carbon::parse($from)->format('m'), Carbon::parse($to)->format('m')];
        $year = Carbon::parse($from)->format('Y');
        $months = [];
        $period = CarbonPeriod::create($from, '1 month', $to);
        foreach ($period as $date) {
            $months[] = $date->format('F'); // 'F' gives full month name, use 'm' for month number
        }
        $dates[] = [
            'Month' => implode(', ', $months),
            'Year' => $year,
        ];
        $shifts = Shift::when(!empty($visit['shifts']), fn($q) => $q->whereIntegerInRaw("shifts.id", $visit['shifts']))->get();
        $accountTypes = AccountType::select('id')->whereIntegerInRaw("account_types.id", $visit['accountTypes'])
            ->when(!empty($visit['shifts']), fn($q) => $q->whereIntegerInRaw("account_types.shift_id", $visit['shifts']))->pluck('id')->toArray();
        $specialities = Speciality::select('id')->whereIntegerInRaw("specialities.id", $visit['specialities'])->pluck('id')->toArray();
        $lines = Line::when(!empty($visit['lines']), fn($q) => $q->whereIntegerInRaw("lines.id", $visit['lines']))->get();
        $division_type = DivisionType::where('last_level', '=', 1)->value('id');
        $filtered = new Collection([]);
        $data = new Collection([]);
        $days = Setting::where('key', 'fixed_working_days')->value('value');
        $classes = collect([]);
        $fields = collect(['employee', 'code', 'line',]);
        foreach ($shifts as $shift) {
            $fields = $fields->merge([
                $shift->name . '_docs',
                $shift->name . '_cov_docs',
                $shift->name . '_uncov_docs',
                $shift->name . '_coverage',
                $shift->name . '_w_days',
                $shift->name . '_vacs',
                $shift->name . '_ow',
                $shift->name . '_requests',
                $shift->name . '_net_days',
                $shift->name . '_daily_target',
                $shift->name . '_monthly_target',
                $shift->name . '_actual',
                $shift->name . '_actual_days',
                $shift->name . '_achieve',
            ]);
        }
        if ($visit['view'] == 'Advanced') {
            $fields = $fields->merge(['total_docs', 'meet', 'below', 'above', '%(meet)', '%(meet+above)', 'plans', 'achieved_plans', 'achievement_gap', 'achieved_ratio']);
        } else {
            $classes = Classes::select('id', 'name')->when(!empty($visit['classes']), fn($q) => $q->whereIntegerInRaw("classes.id", $visit['classes']))->get();
            foreach ($classes as $class) {
                $fields = $fields->merge([$class->name . '_total_docs', $class->name . '_target', $class->name . '_visits', $class->name . '_achieve']);
            }
        }
        foreach ($lines as $line) {
            if ($visit['position'] != null) {
                $filtered = User::whereIntegerInRaw("id", $visit['users'])->get();
            } else {
                $users = $line->users($from, $to)->when(!empty($visit['users']), fn($q) => $q->whereIntegerInRaw("line_users.user_id", $visit['users']))->get();
                $filtered = $filtered->merge($authUser->filterUsers($line, $users, $visit, $from, $to));
            }
        }
        $filtered->unique('id')->values()->each(function ($user) use ($days, $classes, $data, $visit, $from, $to, $month, $year, $division_type, $shifts, $accountTypes, $specialities) {
            $data = $data->push($this->statistics2($user,  $from, $to, $month, $year, $visit, $division_type, $shifts, $classes, $days, $accountTypes, $specialities));
        });
        return response()->json([
            'data' => $data->unique("id")->values(),
            'fields' => $fields,
            'dates' => $dates
        ]);
    }


    public function statistics2($user, $from, $to, $months, $year, $visit, $division_type, $shifts, $classes, $days, $accountTypes, $specialities)
    {
        $lines = collect([]);
        $objLines = $user->lines($from, $to);

        $belowUsers = collect([]);
        if ($visit['result_by'] == 'filtered') {
            $lines = $objLines->whereIntegerInRaw('lines.id', $visit['lines'])->get()->pluck('id')->toArray();
        } else {
            $lines = $objLines->get()->pluck('id')->toArray();
        }
        $lineFirstId = !empty($lines) ? $lines[0] : null;
        $divisions = $user->allBelowDivisions(from: $from, to: $to)->whereIn('line_id', $lines)->where('is_kol', 0)
            ->where('division_type_id', $division_type)->unique('id')->pluck('id')->toArray();
        collect($divisions)->each(function ($division) use ($belowUsers, $lines, $from, $to) {
            $div = LineDivision::find($division);
            $belowUsers = $belowUsers->push($div->users($from, $to)->whereIntegerInRaw('line_id', $lines)->get());
        });
        $belowUsers = $belowUsers->collapse()->unique('id')->values();
        $directDivisions = $user->divisions($from, $to)->whereIntegerInRaw('line_divisions.line_id', $lines);
        $pivotDivision = $directDivisions->first()?->pivot;
        // throw new CrmException($pivotDivision);
        $userFromDate = $from;
        $userToDate = $to;
        if ($pivotDivision) {
            $userFromDate = $pivotDivision->from_date > $from ? Carbon::parse($pivotDivision->from_date) : $from;
            $userToDate = $pivotDivision->to_date && $pivotDivision->to_date < $to ? Carbon::parse($pivotDivision->to_date) : $to;
        }
        $data = collect([
            'id' => $user->id,
            'line' => $objLines->pluck('name')->implode(' , '),
            // 'line_id' => $objLines->pluck('id')->implode(' , '),
            'employee' => $user->fullname,
            'code' => $user->emp_code ?? '',
            'color' => $user->divisions($from, $to)->where('is_kol', 0)->first()?->DivisionType->color,
        ]);
        $freq = [];
        $meet = 0;
        $belows = 0;
        $above = 0;
        $plansCount = 0;
        $achievedPlans = 0;
        $achievementGap = 0;
        $totalDocs = 0;
        $requests = 0;
        foreach ($shifts as $shift) {
            $shiftDoctorCount = 0;
            $coveredDoctors = 0;
            $w_days = $days > 0 ? $days : CallRate::calculateWorkingDays($userFromDate, $userToDate, $shift->id, $lineFirstId);
            $vacs = 0;
            $ow = 0;
            $actualDays = 0;
            $net_days = 0;
            $visitsCount = 0;
            $cr = 0;
            foreach ($belowUsers as $below) {
                $belowUser = User::find($below->id);
                $belowDivs = $belowUser->allBelowDivisions(from: $from, to: $to)->whereIn('line_id', $lines)
                    ->where('division_type_id', $division_type)
                    ->where('is_kol', 0)
                    ->pluck('id')->toArray();
                $daily_target = CallRate::select('id', 'line_id', 'division_id', 'shift_id', 'call_rate', 'date')
                    ->whereBetween(DB::raw("(DATE_FORMAT(crm_call_rates.date,'%m'))"), $months)
                    ->whereYear('call_rates.date', $year)
                    ->where('shift_id', $shift->id);
                if (!empty($belowDivs)) {
                    $daily_target = $daily_target->whereIntegerInRaw('division_id', $belowDivs);
                }
                $daily_target = $daily_target->first()?->call_rate;
                //Coverage 
                $doctors = (new DoctorService)->getDoctorsWithFrequency(
                    lines: $lines,
                    divisions: $belowDivs,
                    from: $from,
                    to: $to,
                    months: $months,
                    year: $year,
                    specialities: $specialities,
                    accountTypes: $accountTypes,
                    shift: $shift->id
                );
                // throw new CrmException($doctors);
                $actualsPerShift = (new ActualService)->getActuals(
                    object: $below,
                    column: 'users.id',
                    from: $from,
                    to: $to,
                    shifts: [$shift->id],
                    specialities: $specialities,
                    accountTypes: $accountTypes
                );
                $listActuals = $actualsPerShift->whereIn('account_dr_id', $doctors->pluck('id')->toArray());
                $plans = (new PlanService)->getPlans($below, 'users.id', $from, $to, [$shift->id], $lines, $specialities, $accountTypes);
                $plansCount += $plans->count();
                $achievedPlans += $plans->whereNotNull('plan_id')->count();
                $shiftDoctorCount += $doctors->count();
                $totalDocs += $shiftDoctorCount;
                $callRateActualsPerShift = $actualsPerShift;
                $coveredDoctors += $listActuals->unique('account_dr_id')->count();

                // Call Rate
                $vacs = Vacation::getVacationsDatePerPeriod(
                    user: $below,
                    from: $from,
                    to: $to,
                    month: $months,
                    year: $year,
                    shifts: [$shift->id],
                    line_id: $lineFirstId
                );
                $requests = CommercialRequest::commercialsPerPeriod($below->id, $from, $to, [$shift->id]);
                $ow = $this->ow($below, $from, $to, $shift->id, $lineFirstId);
                $net_days = $w_days - $vacs - $ow - $requests;
                $monthly_target = $net_days * $daily_target;
                if ($shift->id == 1) {
                    $visitsCount += $callRateActualsPerShift->unique(function ($item) {
                        return $item->date . $item->account_id;
                    })->values()->count();
                } else {
                    $visitsCount += $callRateActualsPerShift->count();
                }
                $actualDays = $actualsPerShift->pluck('date')->unique()->count();
                $cr += $monthly_target ? $visitsCount / $monthly_target : 0;
                if ($visit['view'] == 'Advanced') {
                    $freq = $this->advancedFrequency($doctors, $listActuals);
                    $meet += $freq['meet'];
                    $belows += $freq['below'];
                    $above += $freq['above'];
                }
                if ($visit['view'] == 'Classic') {
                    foreach ($classes as $class) {
                        $doctorClass = 0;
                        $target = 0;
                        $doctorClassActualCount = 0;
                        $freq = $this->classicFrequency($doctors, $listActuals, $class);
                        $doctorClass = $freq['doctorClass'];
                        $target = $freq['total'];
                        $doctorClassActualCount = $freq['actualCount'];
                        $data = $data->put($class->name . '_total_docs', $doctorClass);
                        $data = $data->put($class->name . '_target', $target);
                        $data = $data->put($class->name . '_visits', $doctorClassActualCount);
                        $achieveClass = $doctorClass > 0 ? round($doctorClassActualCount / $doctorClass * 100, 2) . ' %' : 0 . ' %';
                        $data = $data->put($class->name . '_achieve', $achieveClass);
                    }
                }
            }

            if (!$user->divisions()->where('is_kol', 0)->first()?->isLastLevelType()) {
                $vacs = Vacation::getVacationsDatePerPeriod(
                    user: $user,
                    from: $from,
                    to: $to,
                    month: $months,
                    year: $year,
                    shifts: [$shift->id],
                    line_id: $lineFirstId
                );
                $requests = CommercialRequest::commercialsPerPeriod($user->id, $from, $to, [$shift->id]);
                $ow = $this->ow($user, $from, $to, $shift->id, $lineFirstId);
                $net_days = $w_days - $vacs - $ow;
            }
            $achieve = count($belowUsers) != 0 ? round((($cr / count($belowUsers)) * 100), 2) . ' %' : 0 . ' %';
            $uncov =  $shiftDoctorCount - $coveredDoctors;
            $data = $data->put($shift->name . '_docs', $shiftDoctorCount >= 0 ? $shiftDoctorCount : 0);
            $data = $data->put($shift->name . '_cov_docs', $coveredDoctors >= 0 ? $coveredDoctors : 0);
            $data = $data->put($shift->name . '_uncov_docs', $uncov >= 0 ? $uncov : 0);
            $data = $data->put($shift->name . '_coverage', $shiftDoctorCount > 0 ? round(($coveredDoctors / $shiftDoctorCount) * 100, 2) . ' %' : 0 . ' %');
            $data = $data->put($shift->name . '_w_days', $w_days);
            $data = $data->put($shift->name . '_vacs', $vacs);
            $data = $data->put($shift->name . '_ow', $ow);
            $data = $data->put($shift->name . '_requests', $requests);
            $data = $data->put($shift->name . '_net_days', $net_days);
            $data = $data->put($shift->name . '_daily_target', $daily_target ?? 0);
            $data = $data->put($shift->name . '_monthly_target', $monthly_target ?? 0);
            $data = $data->put($shift->name . '_actual', $visitsCount);
            $data = $data->put($shift->name . '_actual_days', $actualDays);
            $data = $data->put($shift->name . '_achieve', $achieve);
        }
        $achievementGap = $plansCount > 0 ? $plansCount - $achievedPlans : 0;
        $data = $data->put('total_docs', $totalDocs);
        $data = $data->put('meet', $meet);
        $data = $data->put('below', $belows);
        $data = $data->put('above', $above);
        $ratio1 = $totalDocs != 0 ? round(($meet / $totalDocs) * 100, 2) : 0;
        $ratio2 = $totalDocs != 0 ? round((($meet + $above) / $totalDocs) * 100, 2) : 0;
        $data = $data->put('%(meet)', $ratio1 . ' %');
        $data = $data->put('%(meet+above)', $ratio2 . ' %');
        $data = $data->put('plans', $plansCount);
        $data = $data->put('achieved_plans', $achievedPlans);
        $data = $data->put('achievement_gap', $achievementGap);
        $data = $data->put('achieved_ratio', $plansCount > 0 ? round($achievedPlans / $plansCount * 100, 2) . '%' : 0 . '%');
        $data = $data->put('%(meet+above)', $ratio2 . ' %');
        return $data;
    }

    private function ow($object, $from, $to, $shift, $lineId)
    {
        $count = 0.0;
        $ow = OwActualVisit::select(
            'ow_actual_visits.id',
            'ow_actual_visits.user_id',
            'ow_actual_visits.date',
            'ow_actual_visits.shift_id',
            'ow_actual_visits.ow_type_id'
        )
            ->leftJoin('office_work_types', 'ow_actual_visits.ow_type_id', 'office_work_types.id')
            ->where('office_work_types.with_deduct', '1')
            ->where('user_id', $object->id)->whereBetween('date', [$from, $to]);
        if ($shift) {
            if ($shift == 3) $shift = 2;
            $ow = $ow->where(fn($q) => $q->where('shift_id', $shift)->orWhereNull('shift_id'));
        }

        $ow = $ow->get()->filter(function ($ow) use ($from, $to, $shift, $lineId) {
            return !OffDay::isOffDay($from, $to, $ow->date, $shift, $lineId);
        })->each(function ($office) use (&$count, $shift) {
            if ($shift) {
                $count += 1.0;
            } else {
                $count = $office->shift_id == null
                    ? $count += 1.0
                    : $count += 0.5;
            }
        });
        return $count ?? 0;
    }

    public function advancedFrequency($doctors, $actuals)
    {
        $meet = 0;
        $above = 0;
        $below = 0;
        $doctors->each(function ($doctor) use (
            $actuals,
            &$meet,
            &$below,
            &$above,
        ) {
            $actual = $actuals->where('account_dr_id', $doctor->id)->count();
            if ((int)$doctor->frequency == 0) {
                $below++;
            } else {
                if ((int)$doctor->frequency === $actual) $meet++;
                if ((int)$doctor->frequency > $actual) $below++;
                if ((int)$doctor->frequency < $actual) $above++;
            }
        });
        return array('below' => $below, 'meet' => $meet, 'above' => $above);
    }
    public function classicFrequency($doctors, $actuals, $class)
    {
        $total = 0;
        $actualCount = 0;
        $doctorClasses = $doctors->where('doc_class_id', $class->id);
        // $doctorIds = $doctorClasses->pluck('id')->unique()->toArray();
        // $actuals = $actuals->whereIn('account_dr_id', $doctorIds);
        $doctorClasses->each(function ($doctor) use ($actuals, &$total, &$actualCount, $class) {
            $frequency = (int)$doctor->frequency;
            $total += $doctor->frequency;
            $actual = $actuals->where('account_dr_id', $doctor->id)->count();
            if ($actual && $actual > $frequency) $actualCount += $frequency;
            if ($actual && $actual <= $frequency) $actualCount += $actual;
        });
        return array(
            'doctorClass' => $doctorClasses->count(),
            'total' => $total,
            'actualCount' => $actualCount,
        );
    }
    public function post(Request $request)
    {
        $performanceData = $request->data;
        $filters = $request->filters;
        $averageShiftData = [];
        $kpi = Kpi::where('name', 'Call Rate')->first();
        foreach ($performanceData as $item) {
            if ($item['line_id'] == 9) {
                $average = floatval(str_replace('%', '', $item['PH_achieve']));
                // Add to new array
                $averageShiftData[] = [
                    'id' => $item['id'],
                    'call_rate' => round($average, 2)
                ];
            } else {
                // Remove '%' and convert to float
                $am = floatval(str_replace('%', '', $item['AM_achieve']));
                $pm = floatval(str_replace('%', '', $item['PM_achieve']));
                $other = floatval(str_replace('%', '', $item['PH_achieve']));

                // Calculate average
                $average = ($am + $pm + $other) / 3;

                // Add to new array
                $averageShiftData[] = [
                    'id' => $item['id'],
                    'call_rate' => round($average, 2)
                ];
            }
        }
        // unset($item);
        $from = Carbon::parse($filters['fromDate'])->startOfMonth()->toDateString();
        $to = Carbon::parse($filters['toDate'])->endOfMonth()->toDateString();
        $period = CarbonPeriod::create($from, '1 month', $to);
        $this->postVisitKpisService->post(KPITypes::CALL_RATE, collect($averageShiftData), $period, $kpi);
        return $this->respondSuccess();
    }

    public function fastPost(Request $request)
    {
        /**@var User authUser */
        $authUser = Auth::user();
        $visit = $request->kpisFilter;
        // throw new CrmException($visit);
        $from = Carbon::parse($visit['fromDate'])->startOfDay();
        $to = Carbon::parse($visit['toDate'])->endOfDay();
        $month = [Carbon::parse($from)->format('m'), Carbon::parse($to)->format('m')];
        $year = Carbon::parse($from)->format('Y');
        $months = [];
        $period = CarbonPeriod::create($from, '1 month', $to);
        foreach ($period as $date) {
            $months[] = $date->format('F'); // 'F' gives full month name, use 'm' for month number
        }
        $dates[] = [
            'Month' => implode(', ', $months),
            'Year' => $year,
        ];
        $shifts = Shift::when(!empty($visit['shifts']), fn($q) => $q->whereIntegerInRaw("shifts.id", $visit['shifts']))->get();
        $accountTypes = AccountType::select('id')->whereIntegerInRaw("account_types.id", $visit['accountTypes'])
            ->when(!empty($visit['shifts']), fn($q) => $q->whereIntegerInRaw("account_types.shift_id", $visit['shifts']))->pluck('id')->toArray();
        $specialities = Speciality::select('id')->whereIntegerInRaw("specialities.id", $visit['specialities'])->pluck('id')->toArray();
        $lines = Line::when(!empty($visit['lines']), fn($q) => $q->whereIntegerInRaw("lines.id", $visit['lines']))->get();
        $division_type = DivisionType::where('last_level', '=', 1)->value('id');
        $filtered = new Collection([]);
        $data = new Collection([]);
        $days = Setting::where('key', 'fixed_working_days')->value('value');
        $classes = collect([]);
        $fields = collect(['employee', 'code', 'line']);
        foreach ($shifts as $shift) {
            $fields = $fields->merge([
                $shift->name . '_w_days',
                $shift->name . '_vacs',
                $shift->name . '_ow',
                $shift->name . '_requests',
                $shift->name . '_net_days',
                $shift->name . '_daily_target',
                $shift->name . '_monthly_target',
                $shift->name . '_actual',
                $shift->name . '_actual_days',
                $shift->name . '_achieve',
            ]);
        }
        foreach ($lines as $line) {
            if ($visit['position'] != null) {
                $filtered = User::whereIntegerInRaw("id", $visit['users'])->get();
            } else {
                $users = $line->users($from, $to)->when(!empty($visit['users']), fn($q) => $q->whereIntegerInRaw("line_users.user_id", $visit['users']))->get();
                $filtered = $filtered->merge($authUser->filterUsers($line, $users, $visit, $from, $to));
            }
        }
        $filtered->unique('id')->values()->each(function ($user) use ($days, $classes, $data, $visit, $from, $to, $month, $year, $division_type, $shifts, $accountTypes, $specialities) {
            $data = $data->push($this->statistics($user,  $from, $to, $month, $year, $visit, $division_type, $shifts, $classes, $days, $accountTypes, $specialities));
        });
        return response()->json([
            'data' => $data->unique("id")->values(),
            'fields' => $fields,
            'dates' => $dates
        ]);
    }

    public function statistics($user, $from, $to, $months, $year, $visit, $division_type, $shifts, $classes, $days, $accountTypes, $specialities)
    {
        $lines = collect([]);
        $objLines = $user->lines($from, $to);
        $belowUsers = collect([]);
        if ($visit['result_by'] == 'filtered') {
            $lines = $objLines->whereIntegerInRaw('lines.id', $visit['lines'])->get()->pluck('id')->toArray();
        } else {
            $lines = $objLines->get()->pluck('id')->toArray();
        }
        $lineFirstId = !empty($lines) ? $lines[0] : null;
        $divisions = $user->allBelowDivisions(from: $from, to: $to)->whereIn('line_id', $lines)->where('is_kol', 0)
            ->where('division_type_id', $division_type)->unique('id')->pluck('id')->toArray();
        collect($divisions)->each(function ($division) use ($belowUsers, $lines, $from, $to) {
            $div = LineDivision::find($division);
            $belowUsers = $belowUsers->push($div->users($from, $to)->whereIntegerInRaw('line_id', $lines)->get());
        });
        $belowUsers = $belowUsers->collapse()->unique('id')->values();
        $data = collect([
            'id' => $user->id,
            'line' => $objLines->pluck('name')->implode(' , '),
            'line_id' => $lineFirstId,
            'employee' => $user->fullname,
            'code' => $user->emp_code ?? '',
            'color' => $user->divisions($from, $to)->where('is_kol', 0)->first()?->DivisionType->color,
        ]);
        foreach ($shifts as $shift) {
            $w_days = $days > 0 ? $days : CallRate::calculateWorkingDays($from, $to, $shift->id, $lineFirstId);
            $vacs = 0;
            $ow = 0;
            $actualDays = 0;
            $net_days = 0;
            $visitsCount = 0;
            $cr = 0;
            foreach ($belowUsers as $below) {
                $belowUser = User::find($below->id);
                $belowDivs = $belowUser->allBelowDivisions(from: $from, to: $to)->whereIn('line_id', $lines)
                    ->where('division_type_id', $division_type)
                    ->where('is_kol', 0)
                    ->pluck('id')->toArray();
                $daily_target = CallRate::select('id', 'line_id', 'division_id', 'shift_id', 'call_rate', 'date')
                    ->whereBetween(DB::raw("(DATE_FORMAT(crm_call_rates.date,'%m'))"), $months)
                    ->whereYear('call_rates.date', $year)
                    ->where('shift_id', $shift->id);
                if (!empty($belowDivs)) {
                    $daily_target = $daily_target->whereIntegerInRaw('division_id', $belowDivs);
                }
                $daily_target = $daily_target->first()?->call_rate;

                // throw new CrmException($doctors);
                $actualsPerShift = (new ActualService)->getActuals(
                    object: $below,
                    column: 'users.id',
                    from: $from,
                    to: $to,
                    shifts: [$shift->id],
                    specialities: $specialities,
                    accountTypes: $accountTypes
                );
                $callRateActualsPerShift = $actualsPerShift;

                // Call Rate
                $vacs = Vacation::getVacationsDatePerPeriod(
                    user: $below,
                    from: $from,
                    to: $to,
                    month: $months,
                    year: $year,
                    shifts: [$shift->id],
                    line_id: $lineFirstId
                );
                $requests = CommercialRequest::commercialsPerPeriod($below->id, $from, $to, [$shift->id]);
                $ow = $this->ow($below, $from, $to, $shift->id, $lineFirstId);
                $net_days = $w_days - $vacs - $ow - $requests;
                $monthly_target = $net_days * $daily_target;
                // if ($shift->id == 1) {
                //     $visitsCount += $callRateActualsPerShift->unique(function ($item) {
                //         return $item->date . $item->account_id;
                //     })->values()->count();
                // } else {
                $visitsCount += $callRateActualsPerShift->count();
                // }
                $actualDays = $actualsPerShift->pluck('date')->unique()->count();
                $cr += $monthly_target ? $visitsCount / $monthly_target : 0;
            }

            if (!$user->divisions()->where('is_kol', 0)->first()?->isLastLevelType()) {
                $vacs = Vacation::getVacationsDatePerPeriod(
                    user: $user,
                    from: $from,
                    to: $to,
                    month: $months,
                    year: $year,
                    shifts: [$shift->id],
                    line_id: $lineFirstId
                );
                $requests = CommercialRequest::commercialsPerPeriod($user->id, $from, $to, [$shift->id]);
                $ow = $this->ow($user, $from, $to, $shift->id, $lineFirstId);
                $net_days = $w_days - $vacs - $ow;
            }
            $achieve = count($belowUsers) != 0 ? round((($cr / count($belowUsers)) * 100), 2) . ' %' : 0 . ' %';
            $data = $data->put($shift->name . '_w_days', $w_days);
            $data = $data->put($shift->name . '_vacs', $vacs);
            $data = $data->put($shift->name . '_ow', $ow);
            $data = $data->put($shift->name . '_requests', $requests);
            $data = $data->put($shift->name . '_net_days', $net_days);
            $data = $data->put($shift->name . '_daily_target', $daily_target ?? 0);
            $data = $data->put($shift->name . '_monthly_target', $monthly_target ?? 0);
            $data = $data->put($shift->name . '_actual', $visitsCount);
            $data = $data->put($shift->name . '_actual_days', $actualDays);
            $data = $data->put($shift->name . '_achieve', $achieve);
        }

        return $data;
    }
}
