<?php

namespace App\Providers;

use App\Services\Structure\Repositories\LineDivisionRepository;
use App\Services\Structure\Repositories\LineDivisionRepositoryInterface;
use App\Services\Sales\Ceiling\Strategies\Distribution\Algorithms\{
    HierarchicalChainDistributionAlgorithm,
    HierarchicalDistributionService,
    SimpleDistributionAlgorithm,
    SplitDistributionAlgorithm
};
use App\Services\Sales\Ceiling\Strategies\Distribution\Contracts\{
    TransactionManagerInterface,
    SettingsProviderInterface,
    LimitCalculatorInterface,
    SaleRepositoryInterface,
    SalesServiceFactoryInterface
};
use App\Services\Sales\Ceiling\Strategies\Distribution\Services\{
    TransactionManager,
    SalesSettingsProvider,
    LimitCalculator,
    SaleDetailFactory,
    SaleCreator,
    SaleRepository,
    SalesServiceFactory
};
use App\Services\Sales\Ceiling\Strategies\Distribution\{
    DistributionStrategyFactory,
    DistributionType,
    PrivatePharmacyStrategy,
    StoreStrategy,
    LocalChainStrategy
};
use App\Services\Enums\SaleDistribution;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;

/**
 * Octane-optimized Service Provider for Distribution Strategy dependencies
 *
 * This provider is designed to work efficiently with Laravel Octane by:
 * - Avoiding circular dependencies
 * - Managing stateful services properly
 * - Using appropriate singleton vs factory patterns
 * - Minimizing memory usage in long-running processes
 */
class DistributionStrategyServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register(): void
    {
        // Register core stateless services as singletons (safe for Octane)
        $this->registerStatelessSingletons();

        // Register stateful services as factories (new instance per request)
        $this->registerStatefulFactories();

        // Register algorithms with lazy loading to avoid circular dependencies
        $this->registerAlgorithmsWithLazyLoading();

        // Register strategies with proper lifecycle management
        $this->registerStrategiesForOctane();

        // Register the main factory
        $this->registerDistributionFactory();
    }

    /**
     * Register stateless services as singletons (Octane-safe)
     *
     * These services don't hold request-specific state and can be safely
     * shared across requests in Octane
     */
    private function registerStatelessSingletons(): void
    {
        // Repository interfaces - typically stateless
        $this->app->singleton(LineDivisionRepositoryInterface::class, LineDivisionRepository::class);
        $this->app->singleton(SaleRepositoryInterface::class, SaleRepository::class);

        // Settings provider - should be stateless
        $this->app->singleton(SettingsProviderInterface::class, SalesSettingsProvider::class);

        // Utility services - stateless
        $this->app->singleton(LimitCalculatorInterface::class, LimitCalculator::class);
        $this->app->singleton(SaleDetailFactory::class);
        $this->app->singleton(SaleCreator::class);
        $this->app->singleton(HierarchicalDistributionService::class);
    }

    /**
     * Register stateful services as factories (new instance per request)
     *
     * These services might hold request-specific state and should be
     * recreated for each request in Octane
     */
    private function registerStatefulFactories(): void
    {
        // Transaction manager - definitely stateful
        $this->app->bind(TransactionManagerInterface::class, TransactionManager::class);

        // Sales service factory - should create fresh instances
        $this->app->bind(SalesServiceFactoryInterface::class, SalesServiceFactory::class);

        // SalesService instances - always create fresh instances
        $this->app->bind('SalesService.Normal', function ($app) {
            return $app->make(SalesServiceFactoryInterface::class)
                ->createNormal();
        });

        $this->app->bind('SalesService.Direct', function ($app) {
            return $app->make(SalesServiceFactoryInterface::class)
                ->createDirect();
        });
    }

    /**
     * Register algorithms with lazy loading to prevent circular dependencies
     *
     * Using closures to delay instantiation until actually needed
     */
    private function registerAlgorithmsWithLazyLoading(): void
    {
        $this->app->bind(SimpleDistributionAlgorithm::class, function ($app) {
            return new SimpleDistributionAlgorithm(
                $app->make(SaleDetailFactory::class),
                $app->make(SalesServiceFactoryInterface::class)
            );
        });

        $this->app->bind(SplitDistributionAlgorithm::class, function ($app) {
            return new SplitDistributionAlgorithm(
                $app->make(SaleDetailFactory::class),
                $app->make(SalesServiceFactoryInterface::class),
                config('distribution.split_primary_percentage', 0.9),
                config('distribution.split_secondary_percentage', 0.1)
            );
        });

        $this->app->bind(HierarchicalChainDistributionAlgorithm::class, function ($app) {
            return new HierarchicalChainDistributionAlgorithm(
                $app->make(SaleDetailFactory::class),
                $app->make(HierarchicalDistributionService::class),
                $app->make(SalesServiceFactoryInterface::class)
            );
        });
    }

    /**
     * Register strategies optimized for Octane usage
     *
     * Each strategy is bound as a factory to ensure fresh instances
     * and avoid state leakage between requests
     */
    private function registerStrategiesForOctane(): void
    {
        $this->app->bind(PrivatePharmacyStrategy::class, function ($app) {
            return new PrivatePharmacyStrategy(
                $app->make(TransactionManagerInterface::class),
                $app->make(SettingsProviderInterface::class),
                $app->make(LimitCalculatorInterface::class),
                $app->make(SaleDetailFactory::class),
                $app->make(SaleCreator::class),
                $app->make(SalesServiceFactoryInterface::class)
                    ->createForDistribution(SaleDistribution::NORMAL, DistributionType::PRIVATE_PHARMACY),
                $app->make(SimpleDistributionAlgorithm::class)
            );
        });

        $this->app->bind(StoreStrategy::class, function ($app) {
            return new StoreStrategy(
                $app->make(TransactionManagerInterface::class),
                $app->make(SettingsProviderInterface::class),
                $app->make(LimitCalculatorInterface::class),
                $app->make(SaleDetailFactory::class),
                $app->make(SaleCreator::class),
                $app->make(SalesServiceFactoryInterface::class)
                    ->createForDistribution(SaleDistribution::NORMAL, DistributionType::STORES),
                $app->make(SplitDistributionAlgorithm::class)
            );
        });

        $this->app->bind(LocalChainStrategy::class, function ($app) {
            return new LocalChainStrategy(
                $app->make(TransactionManagerInterface::class),
                $app->make(SettingsProviderInterface::class),
                $app->make(LimitCalculatorInterface::class),
                $app->make(SaleDetailFactory::class),
                $app->make(SaleCreator::class),
                $app->make(SalesServiceFactoryInterface::class)
                    ->createForDistribution(SaleDistribution::NORMAL, DistributionType::LOCAL_CHAINS),
                $app->make(HierarchicalChainDistributionAlgorithm::class)
            );
        });
    }

    /**
     * Register the main distribution factory
     *
     * This is registered as a singleton since it's just a factory
     * and doesn't hold state itself
     */
    private function registerDistributionFactory(): void
    {
        $this->app->singleton(DistributionStrategyFactory::class, function ($app) {
            return new DistributionStrategyFactory(
                $app
            );
        });
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot(): void
    {
        // Register Octane-specific cleanup if needed
        $this->registerOctaneCleanup();
    }

    /**
     * Register Octane-specific cleanup handlers
     *
     * This ensures proper cleanup between requests to prevent memory leaks
     */
    private function registerOctaneCleanup(): void
    {
        if (class_exists(\Laravel\Octane\Events\RequestReceived::class)) {
            // Register cleanup listeners for Octane events
            $this->app['events']->listen(\Laravel\Octane\Events\RequestReceived::class, function ($event) {
                // Clear any caches or reset stateful services if needed
                $this->clearRequestSpecificState();
            });

            $this->app['events']->listen(\Laravel\Octane\Events\RequestTerminated::class, function ($event) {
                // Cleanup after request completion
                $this->performPostRequestCleanup();
            });
        }
    }

    /**
     * Clear any request-specific state
     *
     * Called at the beginning of each request in Octane
     */
    private function clearRequestSpecificState(): void
    {
        // Clear any static caches or reset singleton state if needed
        // Example: Cache::flush('request_specific_cache');

        // Log for debugging in development
        if (app()->environment('local')) {
            Log::debug('DistributionStrategy: Cleared request-specific state for new Octane request');
        }
    }

    /**
     * Perform cleanup after request completion
     *
     * Called after each request in Octane
     */
    private function performPostRequestCleanup(): void
    {
        // Perform any necessary cleanup
        // Example: Clear temporary files, reset counters, etc.

        // Force garbage collection if memory usage is high
        if (memory_get_usage(true) > config('octane.memory_limit', 128 * 1024 * 1024)) {
            gc_collect_cycles();
        }
    }
}
