<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('actual_visits', function (Blueprint $table) {
            $table->string('app_version')->nullable()->after('device_brand');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('actual_visits', function (Blueprint $table) {
            $table->dropColumn('app_version');
        });
    }
};
