<?php

namespace Tests\Unit\Http\Controllers;

use App\Http\Controllers\HelicopterViewReportController;
use App\Services\Reports\HelicopterViewReportService;
use App\User;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Mockery;
use PHPUnit\Framework\TestCase;

/**
 * Unit tests for HelicopterViewReportController
 * 
 * Tests controller logic with mocked dependencies for Laravel Octane compatibility.
 */
class HelicopterViewReportControllerTest extends TestCase
{
    private HelicopterViewReportController $controller;
    private $mockService;
    private User $mockUser;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockService = Mockery::mock(HelicopterViewReportService::class);
        $this->mockUser = Mockery::mock(User::class);

        // Create a real controller instance with mocked service
        $this->controller = new HelicopterViewReportController($this->mockService);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test successful report generation
     */
    public function test_filter_success(): void
    {
        // Mock Auth facade
        Auth::shouldReceive('user')->andReturn($this->mockUser);
        Auth::shouldReceive('id')->andReturn(1);
        
        // Mock Log facade
        Log::shouldReceive('info')->times(2);
        
        // Mock user
        $this->mockUser->shouldReceive('getAttribute')->with('id')->andReturn(1);

        // Create request with valid data
        $requestData = [
            'from_date' => '2024-01-01',
            'to_date' => '2024-01-31',
            'line_ids' => [1, 2, 3]
        ];
        
        $request = Request::create('/helicopter-view-report', 'POST', $requestData);

        // Mock service response
        $expectedReport = [
            'period' => [
                'from_date' => '2024-01-01',
                'to_date' => '2024-01-31'
            ],
            'lines' => [
                ['id' => 1, 'name' => 'Line 1'],
                ['id' => 2, 'name' => 'Line 2']
            ],
            'metrics' => [
                [
                    'line_id' => 1,
                    'line_name' => 'Line 1',
                    'total_accounts' => 75,
                    'total_visit_coverage' => 60,
                    'total_call_rate' => 8.2,
                    'total_frequency' => 3.8,
                    'total_sales_quantities' => 2500,
                    'total_targets_units' => 3000
                ],
                [
                    'line_id' => 2,
                    'line_name' => 'Line 2',
                    'total_accounts' => 75,
                    'total_visit_coverage' => 60,
                    'total_call_rate' => 8.8,
                    'total_frequency' => 4.2,
                    'total_sales_quantities' => 2500,
                    'total_targets_units' => 3000
                ]
            ]
        ];

        $this->mockService->shouldReceive('generateReport')
            ->once()
            ->with(
                Mockery::type(Carbon::class),
                Mockery::type(Carbon::class),
                [1, 2, 3],
                $this->mockUser
            )
            ->andReturn($expectedReport);

        // Execute the method
        $response = $this->controller->filter($request);

        // Assertions
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals($expectedReport, $responseData['data']);
    }

    /**
     * Test validation failure for missing required fields
     */
    public function test_filter_validation_failure_missing_fields(): void
    {
        // Mock Auth facade
        Auth::shouldReceive('id')->andReturn(1);
        
        // Mock Log facade
        Log::shouldReceive('warning')->once();

        // Create request with missing data
        $request = Request::create('/helicopter-view-report', 'POST', []);

        // Execute the method
        $response = $this->controller->filter($request);

        // Assertions
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Validation failed', $responseData['message']);
        $this->assertArrayHasKey('errors', $responseData);
    }

    /**
     * Test validation failure for invalid date range
     */
    public function test_filter_validation_failure_invalid_date_range(): void
    {
        // Mock Auth facade
        Auth::shouldReceive('user')->andReturn($this->mockUser);
        Auth::shouldReceive('id')->andReturn(1);

        // Create request with invalid date range
        $requestData = [
            'from_date' => '2024-01-31',
            'to_date' => '2024-01-01', // to_date before from_date
            'line_ids' => [1, 2, 3]
        ];
        
        $request = Request::create('/helicopter-view-report', 'POST', $requestData);

        // Execute the method
        $response = $this->controller->filter($request);

        // Assertions
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Invalid date range: from_date must be before or equal to to_date', $responseData['message']);
    }

    /**
     * Test validation failure for date range too large
     */
    public function test_filter_validation_failure_date_range_too_large(): void
    {
        // Mock Auth facade
        Auth::shouldReceive('user')->andReturn($this->mockUser);
        Auth::shouldReceive('id')->andReturn(1);

        // Create request with date range > 365 days
        $requestData = [
            'from_date' => '2024-01-01',
            'to_date' => '2025-01-02', // More than 365 days
            'line_ids' => [1, 2, 3]
        ];
        
        $request = Request::create('/helicopter-view-report', 'POST', $requestData);

        // Execute the method
        $response = $this->controller->filter($request);

        // Assertions
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Date range too large: maximum 365 days allowed', $responseData['message']);
    }

    /**
     * Test service exception handling
     */
    public function test_filter_service_exception(): void
    {
        // Mock Auth facade
        Auth::shouldReceive('user')->andReturn($this->mockUser);
        Auth::shouldReceive('id')->andReturn(1);
        
        // Mock Log facade
        Log::shouldReceive('info')->once();
        Log::shouldReceive('error')->once();
        
        // Mock user
        $this->mockUser->shouldReceive('getAttribute')->with('id')->andReturn(1);

        // Create request with valid data
        $requestData = [
            'from_date' => '2024-01-01',
            'to_date' => '2024-01-31',
            'line_ids' => [1, 2, 3]
        ];
        
        $request = Request::create('/helicopter-view-report', 'POST', $requestData);

        // Mock service to throw exception
        $this->mockService->shouldReceive('generateReport')
            ->once()
            ->andThrow(new \Exception('Service error'));

        // Execute the method
        $response = $this->controller->filter($request);

        // Assertions
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(500, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('An error occurred while generating the report', $responseData['message']);
    }

    /**
     * Test getLines method success
     */
    public function test_get_lines_success(): void
    {
        // Mock Auth facade
        Auth::shouldReceive('user')->andReturn($this->mockUser);
        Auth::shouldReceive('id')->andReturn(1);
        
        // Mock Log facade
        Log::shouldReceive('info')->once();
        Log::shouldReceive('error')->andReturn(null);
        
        // Mock user
        $this->mockUser->shouldReceive('getAttribute')->with('id')->andReturn(1);

        // Mock user lines
        $mockLines = collect([
            (object)['id' => 1, 'name' => 'Line 1'],
            (object)['id' => 2, 'name' => 'Line 2'],
            (object)['id' => 3, 'name' => 'Line 3']
        ]);

        $this->mockUser->shouldReceive('userLines')
            ->once()
            ->andReturn($mockLines);

        // Create request
        $request = Request::create('/helicopter-view-report/lines', 'GET');

        // Execute the method
        $response = $this->controller->getLines($request);

        // Assertions
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('lines', $responseData['data']);
        $this->assertCount(3, $responseData['data']['lines']);
        $this->assertEquals('Line 1', $responseData['data']['lines'][0]['name']);
    }

    /**
     * Test getLines method with custom date parameters
     */
    public function test_get_lines_with_custom_dates(): void
    {
        // Mock Auth facade
        Auth::shouldReceive('user')->andReturn($this->mockUser);
        Auth::shouldReceive('id')->andReturn(1);
        
        // Mock Log facade
        Log::shouldReceive('info')->once();
        Log::shouldReceive('error')->andReturn(null);
        
        // Mock user
        $this->mockUser->shouldReceive('getAttribute')->with('id')->andReturn(1);

        // Mock user lines
        $mockLines = collect([
            (object)['id' => 1, 'name' => 'Line 1']
        ]);

        $this->mockUser->shouldReceive('userLines')
            ->once()
            ->andReturn($mockLines);

        // Create request with custom dates
        $request = Request::create('/helicopter-view-report/lines', 'GET', [
            'from_date' => '2024-01-01',
            'to_date' => '2024-01-31'
        ]);

        // Execute the method
        $response = $this->controller->getLines($request);

        // Assertions
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
    }

    /**
     * Test getLines method exception handling
     */
    public function test_get_lines_exception(): void
    {
        // Mock Auth facade
        Auth::shouldReceive('user')->andReturn($this->mockUser);
        Auth::shouldReceive('id')->andReturn(1);
        
        // Mock Log facade
        Log::shouldReceive('error')->once();
        
        // Mock user to throw exception
        $this->mockUser->shouldReceive('userLines')
            ->once()
            ->andThrow(new \Exception('User lines error'));

        // Create request
        $request = Request::create('/helicopter-view-report/lines', 'GET');

        // Execute the method
        $response = $this->controller->getLines($request);

        // Assertions
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(500, $response->getStatusCode());
        
        $responseData = json_decode($response->getContent(), true);
        $this->assertStringContainsString('An error occurred while fetching lines:', $responseData['message']);
    }
}
