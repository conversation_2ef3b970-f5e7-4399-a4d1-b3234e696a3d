<?php

namespace Tests\Unit\Controllers;

use App\DivisionType;
use App\Http\Controllers\PlanVisitApprovalController;
use App\Http\Requests\PlanApprovalRequest;
use App\Models\Expenses\Types\ExpenseType;
use App\Position;
use App\RequestType;
use App\Services\PlanVisitApprovalService;
use App\Services\PlanVisitDataTransformer;
use App\User;
use App\VacationType;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;

/**
 * Unit tests for PlanVisitApprovalController
 * 
 * These tests follow Laravel testing best practices with proper dependency mocking
 * for Laravel Octane compatibility and focus on business logic testing.
 */
class PlanVisitApprovalControllerTest extends TestCase
{
    private PlanVisitApprovalController $controller;
    private $approvalServiceMock;
    private $dataTransformerMock;
    private $userMock;

    protected function setUp(): void
    {
        parent::setUp();

        $this->approvalServiceMock = Mockery::mock(PlanVisitApprovalService::class);
        $this->dataTransformerMock = Mockery::mock(PlanVisitDataTransformer::class);
        $this->userMock = Mockery::mock(User::class);

        $this->controller = new PlanVisitApprovalController(
            $this->approvalServiceMock,
            $this->dataTransformerMock
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function index_returns_successful_response_with_transformed_data()
    {
        // Arrange
        Auth::shouldReceive('user')->once()->andReturn($this->userMock);
        
        $userLines = collect(['line1', 'line2']);
        $this->userMock->shouldReceive('userLines')->once()->andReturn($userLines);

        $types = collect([new VacationType()]);
        $commercialTypes = collect([new RequestType()]);
        $expenseTypes = collect([new ExpenseType()]);

        VacationType::shouldReceive('get')->once()->andReturn($types);
        RequestType::shouldReceive('get')->once()->andReturn($commercialTypes);
        ExpenseType::shouldReceive('get')->once()->andReturn($expenseTypes);

        $expectedData = [
            'lines' => $userLines,
            'types' => $types,
            'commercialTypes' => $commercialTypes,
            'expenseTypes' => $expenseTypes,
            'models' => [
                ['name' => 'Position', 'type' => Position::class],
                ['name' => 'Division Type', 'type' => DivisionType::class]
            ]
        ];

        $transformedResponse = [
            'status' => 'success',
            'lines' => $userLines,
            'types' => $types,
            'models' => $expectedData['models'],
            'commercialTypes' => $commercialTypes,
            'expenseTypes' => $expenseTypes
        ];

        $this->dataTransformerMock
            ->shouldReceive('transformIndexData')
            ->once()
            ->with($expectedData)
            ->andReturn($transformedResponse);

        Log::shouldReceive('info')->once();

        // Act
        $response = $this->controller->index();

        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals($transformedResponse, $response->getData(true));
    }

    /** @test */
    public function index_handles_exception_and_returns_error_response()
    {
        // Arrange
        Auth::shouldReceive('user')->once()->andReturn($this->userMock);
        Auth::shouldReceive('id')->once()->andReturn(1);
        
        $this->userMock->shouldReceive('userLines')->once()->andThrow(new Exception('Database error'));

        Log::shouldReceive('error')->once();

        // Act
        $response = $this->controller->index();

        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(500, $response->getStatusCode());
        $responseData = $response->getData(true);
        $this->assertEquals('error', $responseData['status']);
        $this->assertEquals('Failed to retrieve approval data', $responseData['message']);
    }

    /** @test */
    public function getPlanVisitsForApproval_returns_successful_response()
    {
        // Arrange
        $request = Mockery::mock(PlanApprovalRequest::class);
        $request->flag = 1;
        $request->user = 123;
        $request->from_date = '2023-01-01';
        $request->to_date = '2023-01-31';
        $request->line_id = [1, 2];
        $request->users_id = [123, 456];

        $requestData = [
            'flag' => 1,
            'user' => 123,
            'from_date' => '2023-01-01',
            'to_date' => '2023-01-31',
            'line_id' => [1, 2],
            'users_id' => [123, 456]
        ];

        $serviceData = [
            'plan_visits' => collect([]),
            'ow_plan_visits' => collect([]),
            'reasons' => collect([]),
            'from_date' => '2023-01-01'
        ];

        $transformedResponse = [
            'plans' => collect([]),
            'planFields' => collect(['plan', 'user']),
            'ow' => collect([])
        ];

        $this->approvalServiceMock
            ->shouldReceive('getPlanVisitsForApproval')
            ->once()
            ->with($requestData)
            ->andReturn($serviceData);

        $this->dataTransformerMock
            ->shouldReceive('transformPlanVisitsResponse')
            ->once()
            ->with($serviceData)
            ->andReturn($transformedResponse);

        Auth::shouldReceive('id')->once()->andReturn(1);
        Log::shouldReceive('info')->once();

        // Act
        $response = $this->controller->getPlanVisitsForApproval($request);

        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals($transformedResponse, $response->getData(true));
    }

    /** @test */
    public function getPlanVisitsForApproval_handles_exception_and_returns_error_response()
    {
        // Arrange
        $request = Mockery::mock(PlanApprovalRequest::class);
        $request->shouldReceive('all')->once()->andReturn([]);
        
        $this->approvalServiceMock
            ->shouldReceive('getPlanVisitsForApproval')
            ->once()
            ->andThrow(new Exception('Service error'));

        Auth::shouldReceive('id')->once()->andReturn(1);
        Log::shouldReceive('error')->once();

        // Act
        $response = $this->controller->getPlanVisitsForApproval($request);

        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(500, $response->getStatusCode());
        $responseData = $response->getData(true);
        $this->assertEquals('error', $responseData['status']);
        $this->assertEquals('Failed to retrieve plan visits for approval', $responseData['message']);
    }

    /** @test */
    public function accept_plan_visit_returns_successful_response()
    {
        // Arrange
        $request = new Request();
        $visits = [
            ['visitable_id' => 1, 'visitable_type' => 'PlanVisit', 'reason_id' => null]
        ];
        $request->merge(['visits' => $visits]);

        $serviceResult = ['status' => 'success'];

        $this->approvalServiceMock
            ->shouldReceive('acceptPlanVisits')
            ->once()
            ->with($visits)
            ->andReturn($serviceResult);

        Auth::shouldReceive('id')->once()->andReturn(1);
        Log::shouldReceive('info')->once();

        // Act
        $response = $this->controller->accept_plan_visit($request);

        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals($serviceResult, $response->getData(true));
    }

    /** @test */
    public function accept_plan_visit_returns_error_when_no_visits_provided()
    {
        // Arrange
        $request = new Request();
        $request->merge(['visits' => []]);

        // Act
        $response = $this->controller->accept_plan_visit($request);

        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        $responseData = $response->getData(true);
        $this->assertEquals('error', $responseData['status']);
        $this->assertEquals('No visits provided', $responseData['message']);
    }

    /** @test */
    public function accept_plan_visit_handles_exception_and_returns_error_response()
    {
        // Arrange
        $request = new Request();
        $visits = [['visitable_id' => 1]];
        $request->merge(['visits' => $visits]);

        $this->approvalServiceMock
            ->shouldReceive('acceptPlanVisits')
            ->once()
            ->andThrow(new Exception('Service error'));

        Auth::shouldReceive('id')->once()->andReturn(1);
        Log::shouldReceive('error')->once();

        // Act
        $response = $this->controller->accept_plan_visit($request);

        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(500, $response->getStatusCode());
        $responseData = $response->getData(true);
        $this->assertEquals('error', $responseData['status']);
        $this->assertEquals('Failed to accept plan visits', $responseData['message']);
    }

    /** @test */
    public function reject_plan_visit_returns_successful_response()
    {
        // Arrange
        $request = new Request();
        $plans = [
            ['visitable_id' => 1, 'visitable_type' => 'PlanVisit', 'reason_id' => 1]
        ];
        $request->merge(['plans' => $plans]);

        $serviceResult = ['status' => 'reject'];

        $this->approvalServiceMock
            ->shouldReceive('rejectPlanVisits')
            ->once()
            ->with($plans)
            ->andReturn($serviceResult);

        Auth::shouldReceive('id')->once()->andReturn(1);
        Log::shouldReceive('info')->once();

        // Act
        $response = $this->controller->reject_plan_visit($request);

        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals($serviceResult, $response->getData(true));
    }

    /** @test */
    public function filterOfEmployees_returns_successful_response()
    {
        // Arrange
        $request = new Request();
        $lineIds = [1, 2, 3];
        $request->merge(['line_id' => $lineIds]);

        Auth::shouldReceive('user')->once()->andReturn($this->userMock);

        $filteredUsers = collect([
            (object)['id' => 1, 'name' => 'User 1'],
            (object)['id' => 2, 'name' => 'User 2']
        ]);

        $transformedResponse = [
            'filtered_users' => $filteredUsers
        ];

        $this->approvalServiceMock
            ->shouldReceive('filterEmployees')
            ->once()
            ->with($lineIds, $this->userMock)
            ->andReturn($filteredUsers);

        $this->dataTransformerMock
            ->shouldReceive('transformFilteredEmployees')
            ->once()
            ->with($filteredUsers)
            ->andReturn($transformedResponse);

        $this->userMock->id = 1;
        Log::shouldReceive('info')->once();

        // Act
        $response = $this->controller->filterOfEmployees($request);

        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals($transformedResponse, $response->getData(true));
    }

    /** @test */
    public function filterOfEmployees_returns_error_when_no_line_ids_provided()
    {
        // Arrange
        $request = new Request();
        $request->merge(['line_id' => []]);

        Auth::shouldReceive('user')->once()->andReturn($this->userMock);

        // Act
        $response = $this->controller->filterOfEmployees($request);

        // Assert
        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        $responseData = $response->getData(true);
        $this->assertEquals('error', $responseData['status']);
        $this->assertEquals('No line IDs provided', $responseData['message']);
    }
}
