<?php

namespace Tests\Unit\Services\Reports;

use App\Account;
use App\ActualVisit;
use App\CallRate;
use App\Line;
use App\LineDivision;
use App\Sale;
use App\SaleDetail;
use App\Services\Reports\HelicopterViewReportService;
use App\TargetDetails;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Mockery;
use PHPUnit\Framework\TestCase;

/**
 * Unit tests for HelicopterViewReportService
 * 
 * Tests business logic without database dependencies using Mockery mocking
 * for Laravel Octane compatibility.
 */
class HelicopterViewReportServiceTest extends TestCase
{
    private HelicopterViewReportService $service;
    private User $mockUser;
    private Carbon $fromDate;
    private Carbon $toDate;
    private array $lineIds;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->service = new HelicopterViewReportService();
        $this->mockUser = Mockery::mock(User::class);
        $this->fromDate = Carbon::parse('2024-01-01');
        $this->toDate = Carbon::parse('2024-01-31');
        $this->lineIds = [1, 2, 3];
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test successful report generation for admin user
     */
    public function test_generate_report_success_for_admin_user(): void
    {
        // Mock Auth facade
        Auth::shouldReceive('user')->andReturn($this->mockUser);
        
        // Mock Log facade
        Log::shouldReceive('info')->times(2);
        Log::shouldReceive('debug')->zeroOrMoreTimes();
        Log::shouldReceive('error')->zeroOrMoreTimes();
        
        // Mock user as admin
        $this->mockUser->shouldReceive('hasRole')
            ->with('admin')
            ->andReturn(true);
        $this->mockUser->shouldReceive('hasRole')
            ->with('sub admin')
            ->andReturn(false);
        $this->mockUser->shouldReceive('hasRole')
            ->with('Gemstone Admin')
            ->andReturn(false);
        $this->mockUser->shouldReceive('getAttribute')
            ->with('id')
            ->andReturn(1);

        // Mock Line model using alias pattern
        $mockLines = collect([
            (object)['id' => 1, 'name' => 'Line 1'],
            (object)['id' => 2, 'name' => 'Line 2']
        ]);

        $mockLine = Mockery::mock('alias:' . Line::class);
        $mockLine->shouldReceive('whereIn')->andReturnSelf();
        $mockLine->shouldReceive('where')->andReturnSelf();
        $mockLine->shouldReceive('select')->andReturnSelf();
        $mockLine->shouldReceive('get')->andReturn($mockLines);

        // Mock LineDivision model using alias pattern
        $mockDivisions = collect([
            (object)['id' => 1, 'line_id' => 1, 'name' => 'Division 1'],
            (object)['id' => 2, 'line_id' => 2, 'name' => 'Division 2']
        ]);

        $mockLineDivision = Mockery::mock('alias:' . LineDivision::class);
        $mockLineDivision->shouldReceive('whereIn')->andReturnSelf();
        $mockLineDivision->shouldReceive('where')->andReturnSelf();
        $mockLineDivision->shouldReceive('select')->andReturnSelf();
        $mockLineDivision->shouldReceive('get')->andReturn($mockDivisions);

        // Mock Account model using alias pattern
        $mockAccount = Mockery::mock('alias:' . Account::class);
        $mockAccount->shouldReceive('whereHas')->andReturnSelf();
        $mockAccount->shouldReceive('where')->andReturnSelf();
        $mockAccount->shouldReceive('count')->andReturn(150);

        // Mock ActualVisit model using alias pattern
        $mockActualVisit = Mockery::mock('alias:' . ActualVisit::class);
        $mockActualVisit->shouldReceive('whereIn')->andReturnSelf();
        $mockActualVisit->shouldReceive('whereBetween')->andReturnSelf();
        $mockActualVisit->shouldReceive('distinct')->andReturnSelf();
        $mockActualVisit->shouldReceive('whereNotNull')->andReturnSelf();
        $mockActualVisit->shouldReceive('count')->andReturn(120, 300, 75);

        // Mock CallRate model using alias pattern
        $mockCallRates = collect([
            (object)['call_rate' => 8.5],
            (object)['call_rate' => 9.2]
        ]);

        $mockCallRate = Mockery::mock('alias:' . CallRate::class);
        $mockCallRate->shouldReceive('whereIn')->andReturnSelf();
        $mockCallRate->shouldReceive('whereBetween')->andReturnSelf();
        $mockCallRate->shouldReceive('select')->andReturnSelf();
        $mockCallRate->shouldReceive('get')->andReturn($mockCallRates);

        // Mock Sale model using alias pattern
        $mockSale = Mockery::mock('alias:' . Sale::class);
        $mockSale->shouldReceive('whereHas')->andReturnSelf();
        $mockSale->shouldReceive('whereBetween')->andReturnSelf();
        $mockSale->shouldReceive('sum')->andReturn(5000);

        // Mock SaleDetail model using alias pattern
        $mockSaleDetail = Mockery::mock('alias:' . SaleDetail::class);
        $mockSaleDetail->shouldReceive('whereIn')->andReturnSelf();
        $mockSaleDetail->shouldReceive('whereBetween')->andReturnSelf();
        $mockSaleDetail->shouldReceive('sum')->andReturn(5000);

        // Mock TargetDetails model using alias pattern
        $mockTargetDetails = Mockery::mock('alias:' . TargetDetails::class);
        $mockTargetDetails->shouldReceive('whereIn')->andReturnSelf();
        $mockTargetDetails->shouldReceive('whereBetween')->andReturnSelf();
        $mockTargetDetails->shouldReceive('sum')->andReturn(6000);

        // Execute the method
        $result = $this->service->generateReport(
            $this->fromDate,
            $this->toDate,
            $this->lineIds,
            $this->mockUser
        );

        // Assertions
        $this->assertIsArray($result);
        $this->assertArrayHasKey('period', $result);
        $this->assertArrayHasKey('lines', $result);
        $this->assertArrayHasKey('metrics', $result);
        
        $this->assertEquals('2024-01-01', $result['period']['from_date']);
        $this->assertEquals('2024-01-31', $result['period']['to_date']);
        
        $this->assertCount(2, $result['lines']);
        $this->assertEquals('Line 1', $result['lines'][0]['name']);

        // Check metrics is array of objects (per line)
        $this->assertIsArray($result['metrics']);
        $this->assertCount(2, $result['metrics']);

        // Check first line metrics
        $firstLineMetrics = $result['metrics'][0];
        $this->assertEquals(1, $firstLineMetrics['line_id']);
        $this->assertEquals('Line 1', $firstLineMetrics['line_name']);
        $this->assertArrayHasKey('total_accounts', $firstLineMetrics);
        $this->assertArrayHasKey('total_visit_coverage', $firstLineMetrics);
        $this->assertArrayHasKey('total_call_rate', $firstLineMetrics);
        $this->assertArrayHasKey('total_frequency', $firstLineMetrics);
        $this->assertArrayHasKey('total_sales_quantities', $firstLineMetrics);
        $this->assertArrayHasKey('total_targets_units', $firstLineMetrics);
    }

    /**
     * Test report generation with no accessible lines
     */
    public function test_generate_report_with_no_accessible_lines(): void
    {
        // Mock Auth facade
        Auth::shouldReceive('user')->andReturn($this->mockUser);
        
        // Mock Log facade
        Log::shouldReceive('info')->once();
        Log::shouldReceive('warning')->once();
        
        // Mock user as regular user
        $this->mockUser->shouldReceive('hasRole')->andReturn(false);
        $this->mockUser->shouldReceive('getAttribute')->with('id')->andReturn(1);
        
        // Mock user lines returning empty collection
        $this->mockUser->shouldReceive('lines')
            ->with($this->fromDate, $this->toDate)
            ->andReturnSelf();
        $this->mockUser->shouldReceive('whereIn')
            ->with('lines.id', $this->lineIds)
            ->andReturnSelf();
        $this->mockUser->shouldReceive('select')
            ->with('lines.id', 'lines.name')
            ->andReturnSelf();
        $this->mockUser->shouldReceive('get')
            ->andReturn(collect([]));

        // Execute the method
        $result = $this->service->generateReport(
            $this->fromDate,
            $this->toDate,
            $this->lineIds,
            $this->mockUser
        );

        // Assertions for empty report
        $this->assertIsArray($result);
        $this->assertNull($result['period']['from_date']);
        $this->assertNull($result['period']['to_date']);
        $this->assertEmpty($result['lines']);
        $this->assertEmpty($result['metrics']); // Empty array for no lines
    }

    /**
     * Test frequency calculation with zero unique doctors
     */
    public function test_frequency_calculation_with_zero_unique_doctors(): void
    {
        // Mock Auth facade
        Auth::shouldReceive('user')->andReturn($this->mockUser);
        
        // Mock Log facade
        Log::shouldReceive('info')->times(2);
        Log::shouldReceive('debug')->zeroOrMoreTimes();
        Log::shouldReceive('error')->zeroOrMoreTimes();
        
        // Mock user as admin
        $this->mockUser->shouldReceive('hasRole')->with('admin')->andReturn(true);
        $this->mockUser->shouldReceive('hasRole')->andReturn(false);
        $this->mockUser->shouldReceive('getAttribute')->with('id')->andReturn(1);

        // Mock Line model using alias pattern
        $mockLines = collect([(object)['id' => 1, 'name' => 'Line 1']]);
        $mockLine = Mockery::mock('alias:' . Line::class);
        $mockLine->shouldReceive('whereIn')->andReturnSelf();
        $mockLine->shouldReceive('where')->andReturnSelf();
        $mockLine->shouldReceive('select')->andReturnSelf();
        $mockLine->shouldReceive('get')->andReturn($mockLines);

        // Mock LineDivision model using alias pattern
        $mockLineDivision = Mockery::mock('alias:' . LineDivision::class);
        $mockLineDivision->shouldReceive('whereIn')->andReturnSelf();
        $mockLineDivision->shouldReceive('where')->andReturnSelf();
        $mockLineDivision->shouldReceive('select')->andReturnSelf();
        $mockLineDivision->shouldReceive('get')->andReturn(collect([]));

        // Mock other models with zero results using alias pattern
        $mockAccount = Mockery::mock('alias:' . Account::class);
        $mockAccount->shouldReceive('whereHas')->andReturnSelf();
        $mockAccount->shouldReceive('where')->andReturnSelf();
        $mockAccount->shouldReceive('count')->andReturn(0);

        $mockActualVisit = Mockery::mock('alias:' . ActualVisit::class);
        $mockActualVisit->shouldReceive('whereIn')->andReturnSelf();
        $mockActualVisit->shouldReceive('whereBetween')->andReturnSelf();
        $mockActualVisit->shouldReceive('distinct')->andReturnSelf();
        $mockActualVisit->shouldReceive('whereNotNull')->andReturnSelf();
        $mockActualVisit->shouldReceive('count')->andReturn(0, 0, 0);

        $mockSale = Mockery::mock('alias:' . Sale::class);
        $mockSale->shouldReceive('whereHas')->andReturnSelf();
        $mockSale->shouldReceive('whereBetween')->andReturnSelf();
        $mockSale->shouldReceive('sum')->andReturn(0);

        $mockSaleDetail = Mockery::mock('alias:' . SaleDetail::class);
        $mockSaleDetail->shouldReceive('whereIn')->andReturnSelf();
        $mockSaleDetail->shouldReceive('whereBetween')->andReturnSelf();
        $mockSaleDetail->shouldReceive('sum')->andReturn(0);

        // Execute the method
        $result = $this->service->generateReport(
            $this->fromDate,
            $this->toDate,
            [1],
            $this->mockUser
        );

        // Assertions
        $this->assertIsArray($result['metrics']);
        $this->assertCount(1, $result['metrics']);
        $this->assertEquals(0.0, $result['metrics'][0]['total_frequency']);
    }
}
