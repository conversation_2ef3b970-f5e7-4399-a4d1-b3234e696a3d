<?php

namespace Tests\Unit\Services;

use App\ActualVisit;
use App\Helpers\LogActivity;
use App\Helpers\Notifications\NotificationHelper;
use App\Notifications\OwApprovedNotification;
use App\Notifications\PlanApprovedNotification;
use App\OwActualVisit;
use App\OwPlanVisit;
use App\PlanApprovalDaySetting;
use App\PlanSetting;
use App\PlanVisit;
use App\PlanVisitDetails;
use App\Reason;
use App\Services\FrequencyTypeValidationService;
use App\Services\PlanVisitApprovalService;
use App\User;
use App\VisitProduct;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;

/**
 * Unit tests for PlanVisitApprovalService
 * 
 * These tests follow Laravel testing best practices with proper dependency mocking
 * for Laravel Octane compatibility and focus on business logic testing.
 */
class PlanVisitApprovalServiceTest extends TestCase
{
    private PlanVisitApprovalService $service;
    private $frequencyServiceMock;
    private $userMock;

    protected function setUp(): void
    {
        parent::setUp();

        $this->frequencyServiceMock = Mockery::mock(FrequencyTypeValidationService::class);
        $this->userMock = Mockery::mock(User::class);

        $this->service = new PlanVisitApprovalService($this->frequencyServiceMock);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function getFilteredDate_returns_approval_day_setting_date_when_user_authorized()
    {
        // Arrange
        $userLines = collect([(object)['line_id' => 1]]);
        
        $approvalDayMock = Mockery::mock();
        $approvalDayMock->user_id = 0;
        $approvalDayMock->date = '2023-12-01 10:00:00';

        PlanApprovalDaySetting::shouldReceive('where')
            ->once()
            ->with('line_id', 1)
            ->andReturnSelf();
        PlanApprovalDaySetting::shouldReceive('orderBy')
            ->once()
            ->with('id', 'DESC')
            ->andReturnSelf();
        PlanApprovalDaySetting::shouldReceive('first')
            ->once()
            ->andReturn($approvalDayMock);

        Auth::shouldReceive('id')->once()->andReturn(1);

        // Act
        $result = $this->service->getFilteredDate($userLines);

        // Assert
        $this->assertEquals('2023-12-01 10:00:00', $result);
    }

    /** @test */
    public function getFilteredDate_returns_tomorrow_when_user_not_authorized()
    {
        // Arrange
        $userLines = collect([(object)['line_id' => 1]]);
        
        $approvalDayMock = Mockery::mock();
        $approvalDayMock->user_id = 999; // Different user

        PlanApprovalDaySetting::shouldReceive('where')
            ->once()
            ->with('line_id', 1)
            ->andReturnSelf();
        PlanApprovalDaySetting::shouldReceive('orderBy')
            ->once()
            ->with('id', 'DESC')
            ->andReturnSelf();
        PlanApprovalDaySetting::shouldReceive('first')
            ->once()
            ->andReturn($approvalDayMock);

        Auth::shouldReceive('id')->once()->andReturn(1);

        // Act
        $result = $this->service->getFilteredDate($userLines);

        // Assert
        $expectedDate = Carbon::tomorrow()->format('Y-m-d H:i:s');
        $this->assertEquals($expectedDate, $result);
    }

    /** @test */
    public function getFilteredDate_handles_exception_and_logs_error()
    {
        // Arrange
        $userLines = collect([(object)['line_id' => 1]]);

        PlanApprovalDaySetting::shouldReceive('where')
            ->once()
            ->andThrow(new Exception('Database error'));

        Auth::shouldReceive('id')->twice()->andReturn(1);
        Log::shouldReceive('error')->once();

        // Act
        $result = $this->service->getFilteredDate($userLines);

        // Assert
        $expectedDate = Carbon::tomorrow()->format('Y-m-d H:i:s');
        $this->assertEquals($expectedDate, $result);
    }

    /** @test */
    public function getOwPlanVisits_returns_filtered_results_with_date_range()
    {
        // Arrange
        $fromDate = '2023-01-01';
        $toDate = '2023-01-31';
        $userIds = [1, 2, 3];

        $queryMock = Mockery::mock(Builder::class);
        $resultCollection = collect([
            (object)['id' => 1, 'user' => 'User 1'],
            (object)['id' => 2, 'user' => 'User 2']
        ]);

        OwPlanVisit::shouldReceive('select')
            ->once()
            ->andReturn($queryMock);

        $queryMock->shouldReceive('leftJoin')->times(4)->andReturnSelf();
        $queryMock->shouldReceive('whereIn')
            ->once()
            ->with('ow_plan_visits.user_id', $userIds)
            ->andReturnSelf();
        $queryMock->shouldReceive('where')
            ->once()
            ->with('plan_visit_details.visitable_type', '=', OwPlanVisit::class)
            ->andReturnSelf();
        $queryMock->shouldReceive('whereBetween')
            ->once()
            ->with('ow_plan_visits.day', [$fromDate, $toDate])
            ->andReturnSelf();
        $queryMock->shouldReceive('get')
            ->once()
            ->andReturn($resultCollection);

        Log::shouldReceive('info')->once();

        // Act
        $result = $this->service->getOwPlanVisits($fromDate, $toDate, $userIds);

        // Assert
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertEquals(2, $result->count());
    }

    /** @test */
    public function getOwPlanVisits_handles_exception_and_returns_empty_collection()
    {
        // Arrange
        $fromDate = '2023-01-01';
        $toDate = '2023-01-31';
        $userIds = [1, 2, 3];

        OwPlanVisit::shouldReceive('select')
            ->once()
            ->andThrow(new Exception('Database error'));

        Log::shouldReceive('error')->once();

        // Act
        $result = $this->service->getOwPlanVisits($fromDate, $toDate, $userIds);

        // Assert
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertTrue($result->isEmpty());
    }

    /** @test */
    public function getPlanVisitsForApproval_returns_data_for_single_user_mode()
    {
        // Arrange
        $requestData = [
            'flag' => 1,
            'user' => 123,
            'from_date' => null,
            'to_date' => null,
            'line_id' => null,
            'users_id' => null
        ];

        $planVisitMock = Mockery::mock(Builder::class);
        $planVisits = collect([new PlanVisit()]);
        $owPlanVisits = collect([]);
        $reasons = collect([new Reason()]);

        Reason::shouldReceive('where')
            ->once()
            ->with('request_type', PlanVisit::class)
            ->andReturnSelf();
        Reason::shouldReceive('get')
            ->once()
            ->andReturn($reasons);

        PlanVisit::shouldReceive('where')
            ->once()
            ->with('user_id', 123)
            ->andReturn($planVisitMock);
        $planVisitMock->shouldReceive('whereHas')
            ->once()
            ->andReturnSelf();
        $planVisitMock->shouldReceive('where')
            ->once()
            ->andReturnSelf();
        $planVisitMock->shouldReceive('get')
            ->once()
            ->andReturn($planVisits);

        // Mock the getOwPlanVisits call
        $this->service = Mockery::mock(PlanVisitApprovalService::class)->makePartial();
        $this->service->shouldReceive('getOwPlanVisits')
            ->once()
            ->andReturn($owPlanVisits);

        Log::shouldReceive('info')->once();

        // Act
        $result = $this->service->getPlanVisitsForApproval($requestData);

        // Assert
        $this->assertIsArray($result);
        $this->assertArrayHasKey('plan_visits', $result);
        $this->assertArrayHasKey('ow_plan_visits', $result);
        $this->assertArrayHasKey('reasons', $result);
        $this->assertEquals($planVisits, $result['plan_visits']);
        $this->assertEquals($owPlanVisits, $result['ow_plan_visits']);
        $this->assertEquals($reasons, $result['reasons']);
    }

    /** @test */
    public function getFollowUp_returns_follow_up_information()
    {
        // Arrange
        $actualVisits = collect([
            (object)['id' => 1],
            (object)['id' => 2]
        ]);

        $visitProductMock = Mockery::mock(Builder::class);
        $followUpResult = (object)['follow_up' => 'Follow up notes'];

        VisitProduct::shouldReceive('select')
            ->once()
            ->with('follow_up')
            ->andReturn($visitProductMock);
        $visitProductMock->shouldReceive('whereIntegerInRaw')
            ->once()
            ->with('visit_id', [1, 2])
            ->andReturnSelf();
        $visitProductMock->shouldReceive('whereNotNull')
            ->once()
            ->with('follow_up')
            ->andReturnSelf();
        $visitProductMock->shouldReceive('whereNull')
            ->once()
            ->with('follow_up_flag')
            ->andReturnSelf();
        $visitProductMock->shouldReceive('orderBy')
            ->once()
            ->with('created_at', 'DESC')
            ->andReturnSelf();
        $visitProductMock->shouldReceive('first')
            ->once()
            ->andReturn($followUpResult);

        // Act
        $result = $this->service->getFollowUp($actualVisits);

        // Assert
        $this->assertEquals('Follow up notes', $result);
    }

    /** @test */
    public function getFollowUp_returns_empty_string_when_no_follow_up_found()
    {
        // Arrange
        $actualVisits = collect([
            (object)['id' => 1]
        ]);

        $visitProductMock = Mockery::mock(Builder::class);

        VisitProduct::shouldReceive('select')
            ->once()
            ->with('follow_up')
            ->andReturn($visitProductMock);
        $visitProductMock->shouldReceive('whereIntegerInRaw')
            ->once()
            ->andReturnSelf();
        $visitProductMock->shouldReceive('whereNotNull')
            ->once()
            ->andReturnSelf();
        $visitProductMock->shouldReceive('whereNull')
            ->once()
            ->andReturnSelf();
        $visitProductMock->shouldReceive('orderBy')
            ->once()
            ->andReturnSelf();
        $visitProductMock->shouldReceive('first')
            ->once()
            ->andReturn(null);

        // Act
        $result = $this->service->getFollowUp($actualVisits);

        // Assert
        $this->assertEquals('', $result);
    }

    /** @test */
    public function getFollowUp_handles_exception_and_returns_empty_string()
    {
        // Arrange
        $actualVisits = collect([
            (object)['id' => 1]
        ]);

        VisitProduct::shouldReceive('select')
            ->once()
            ->andThrow(new Exception('Database error'));

        Log::shouldReceive('error')->once();

        // Act
        $result = $this->service->getFollowUp($actualVisits);

        // Assert
        $this->assertEquals('', $result);
    }
}
