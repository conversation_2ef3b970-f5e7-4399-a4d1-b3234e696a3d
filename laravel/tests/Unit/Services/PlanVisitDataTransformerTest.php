<?php

namespace Tests\Unit\Services;

use App\OwPlanVisit;
use App\PlanVisit;
use App\Reason;
use App\Services\FrequencyTypeValidationService;
use App\Services\PlanVisitApprovalService;
use App\Services\PlanVisitDataTransformer;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Mockery;
use Tests\TestCase;

/**
 * Unit tests for PlanVisitDataTransformer
 * 
 * These tests follow Laravel testing best practices with proper dependency mocking
 * for Laravel Octane compatibility and focus on business logic testing.
 */
class PlanVisitDataTransformerTest extends TestCase
{
    private PlanVisitDataTransformer $transformer;
    private $approvalServiceMock;
    private $frequencyServiceMock;

    protected function setUp(): void
    {
        parent::setUp();

        $this->approvalServiceMock = Mockery::mock(PlanVisitApprovalService::class);
        $this->frequencyServiceMock = Mockery::mock(FrequencyTypeValidationService::class);

        $this->transformer = new PlanVisitDataTransformer(
            $this->approvalServiceMock,
            $this->frequencyServiceMock
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function transformOwPlanVisits_returns_transformed_collection()
    {
        // Arrange
        $owPlanVisits = collect([
            (object)[
                'id' => 1,
                'user' => 'John Doe',
                'day' => '2023-12-01',
                'office_work' => 'Meeting',
                'shift' => 'Morning',
                'approval' => null,
                'details' => (object)['approval' => null]
            ],
            (object)[
                'id' => 2,
                'user' => 'Jane Smith',
                'day' => '2023-12-02',
                'office_work' => 'Training',
                'shift' => 'Afternoon',
                'approval' => 1,
                'details' => (object)['approval' => 1]
            ]
        ]);

        $reasons = collect([new Reason()]);

        // Act
        $result = $this->transformer->transformOwPlanVisits($owPlanVisits, $reasons);

        // Assert
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertEquals(1, $result->count()); // Only one should pass the filter (approval is null)
        
        $firstItem = $result->first();
        $this->assertEquals(1, $firstItem['plan']);
        $this->assertEquals('John Doe', $firstItem['user']);
        $this->assertEquals('2023-12-01', $firstItem['day']);
        $this->assertEquals('Meeting', $firstItem['office_work']);
        $this->assertEquals('Morning', $firstItem['shift']);
        $this->assertEquals(OwPlanVisit::class, $firstItem['visitable_type']);
        $this->assertEquals($reasons, $firstItem['reasons']);
        $this->assertNull($firstItem['reason_id']);
    }

    /** @test */
    public function transformOwPlanVisits_handles_exception_and_returns_empty_collection()
    {
        // Arrange
        $owPlanVisits = collect([
            (object)['invalid' => 'data'] // This will cause an error
        ]);
        $reasons = collect([]);

        Log::shouldReceive('error')->once();

        // Act
        $result = $this->transformer->transformOwPlanVisits($owPlanVisits, $reasons);

        // Assert
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertTrue($result->isEmpty());
    }

    /** @test */
    public function transformPlanVisits_returns_transformed_collection()
    {
        // Arrange
        $planVisitMock = Mockery::mock(PlanVisit::class);
        $planVisitMock->id = 1;
        $planVisitMock->visit_date = '2023-12-01';
        $planVisitMock->start_point = 'Office';
        $planVisitMock->account_id = 123;
        $planVisitMock->account_dr_id = 456;
        $planVisitMock->line_id = 789;

        // Mock relationships
        $userMock = (object)['fullname' => 'John Doe'];
        $lineMock = (object)['name' => 'Line A'];
        $divisionMock = (object)['name' => 'Division B'];
        $accountMock = (object)['name' => 'Account C'];
        $doctorMock = (object)['name' => 'Dr. Smith'];
        $detailsMock = (object)['approval' => 1];
        $shiftMock = (object)['name' => 'Morning'];
        $typeMock = (object)['name' => 'Hospital'];

        $planVisitMock->shouldReceive('getAttribute')->with('user')->andReturn($userMock);
        $planVisitMock->shouldReceive('getAttribute')->with('line')->andReturn($lineMock);
        $planVisitMock->shouldReceive('getAttribute')->with('division')->andReturn($divisionMock);
        $planVisitMock->shouldReceive('getAttribute')->with('account')->andReturn($accountMock);
        $planVisitMock->shouldReceive('getAttribute')->with('doctor')->andReturn($doctorMock);
        $planVisitMock->shouldReceive('getAttribute')->with('details')->andReturn($detailsMock);
        $planVisitMock->shouldReceive('getAttribute')->with('shift')->andReturn($shiftMock);

        $accountMock->shouldReceive('pharmacies')->andReturnSelf();
        $accountMock->shouldReceive('count')->andReturn(5);
        $accountMock->type = $typeMock;

        $planVisits = collect([$planVisitMock]);
        $reasons = collect([new Reason()]);
        $fromDate = '2023-12-01';

        $actualVisits = collect([]);

        $this->approvalServiceMock
            ->shouldReceive('getActualVisitsForPlan')
            ->once()
            ->with($planVisitMock)
            ->andReturn($actualVisits);

        $this->approvalServiceMock
            ->shouldReceive('getFollowUp')
            ->once()
            ->with($actualVisits)
            ->andReturn('Follow up notes');

        $this->frequencyServiceMock
            ->shouldReceive('classType')
            ->once()
            ->with(123, 456, 789, $fromDate)
            ->andReturn(3);

        // Act
        $result = $this->transformer->transformPlanVisits($planVisits, $reasons, $fromDate);

        // Assert
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertEquals(1, $result->count());
        
        $firstItem = $result->first();
        $this->assertEquals(1, $firstItem['plan']);
        $this->assertEquals('John Doe', $firstItem['user']);
        $this->assertEquals('2023-12-01', $firstItem['date']);
        $this->assertEquals('Line A', $firstItem['line']);
        $this->assertEquals('Division B', $firstItem['division']);
        $this->assertEquals('Account C', $firstItem['account']);
        $this->assertEquals(5, $firstItem['pharmacies']);
        $this->assertEquals(1, $firstItem['approval']);
        $this->assertEquals(3, $firstItem['f']);
        $this->assertEquals(0, $firstItem['a']); // actualVisits count
        $this->assertEquals('Dr. Smith', $firstItem['doctor']);
        $this->assertEquals('Follow up notes', $firstItem['f_u']);
        $this->assertEquals('Hospital', $firstItem['type']);
        $this->assertEquals('Morning', $firstItem['shift']);
        $this->assertEquals('Office', $firstItem['start']);
        $this->assertEquals(PlanVisit::class, $firstItem['visitable_type']);
        $this->assertEquals($reasons, $firstItem['reasons']);
        $this->assertNull($firstItem['reason_id']);
    }

    /** @test */
    public function transformPlanVisits_handles_exception_and_returns_empty_collection()
    {
        // Arrange
        $planVisits = collect([
            (object)['invalid' => 'data'] // This will cause an error
        ]);
        $reasons = collect([]);
        $fromDate = '2023-12-01';

        Log::shouldReceive('error')->once();

        // Act
        $result = $this->transformer->transformPlanVisits($planVisits, $reasons, $fromDate);

        // Assert
        $this->assertInstanceOf(Collection::class, $result);
        $this->assertTrue($result->isEmpty());
    }

    /** @test */
    public function getPlanFields_returns_expected_fields()
    {
        // Act
        $result = $this->transformer->getPlanFields();

        // Assert
        $this->assertInstanceOf(Collection::class, $result);
        $expectedFields = [
            "s", "plan", "user", "line", "division", "account", 
            "doctor", "type", "pharmacies", "f_u", "shift", 
            "date", "start", "f", "a", "actions"
        ];
        $this->assertEquals($expectedFields, $result->toArray());
    }

    /** @test */
    public function transformPlanVisitsResponse_returns_complete_response()
    {
        // Arrange
        $data = [
            'plan_visits' => collect([]),
            'ow_plan_visits' => collect([]),
            'reasons' => collect([]),
            'from_date' => '2023-12-01'
        ];

        $transformedPlanVisits = collect(['plan1']);
        $transformedOwPlanVisits = collect(['ow1']);
        $planFields = collect(['field1', 'field2']);

        // Mock the transformer methods
        $this->transformer = Mockery::mock(PlanVisitDataTransformer::class)->makePartial();
        $this->transformer->shouldReceive('transformPlanVisits')
            ->once()
            ->andReturn($transformedPlanVisits);
        $this->transformer->shouldReceive('transformOwPlanVisits')
            ->once()
            ->andReturn($transformedOwPlanVisits);
        $this->transformer->shouldReceive('getPlanFields')
            ->once()
            ->andReturn($planFields);

        Log::shouldReceive('info')->once();

        // Act
        $result = $this->transformer->transformPlanVisitsResponse($data);

        // Assert
        $this->assertIsArray($result);
        $this->assertArrayHasKey('plans', $result);
        $this->assertArrayHasKey('planFields', $result);
        $this->assertArrayHasKey('ow', $result);
        $this->assertEquals($transformedPlanVisits, $result['plans']);
        $this->assertEquals($planFields, $result['planFields']);
        $this->assertEquals($transformedOwPlanVisits, $result['ow']);
    }

    /** @test */
    public function transformIndexData_returns_formatted_response()
    {
        // Arrange
        $data = [
            'lines' => collect(['line1']),
            'types' => collect(['type1']),
            'models' => [['name' => 'Model1']],
            'commercialTypes' => collect(['commercial1']),
            'expenseTypes' => collect(['expense1'])
        ];

        // Act
        $result = $this->transformer->transformIndexData($data);

        // Assert
        $this->assertIsArray($result);
        $this->assertEquals('success', $result['status']);
        $this->assertEquals($data['lines'], $result['lines']);
        $this->assertEquals($data['types'], $result['types']);
        $this->assertEquals($data['models'], $result['models']);
        $this->assertEquals($data['commercialTypes'], $result['commercialTypes']);
        $this->assertEquals($data['expenseTypes'], $result['expenseTypes']);
    }

    /** @test */
    public function transformFilteredEmployees_returns_formatted_response()
    {
        // Arrange
        $users = collect([
            (object)['id' => 1, 'name' => 'User 1'],
            (object)['id' => 2, 'name' => 'User 2'],
            (object)['id' => 1, 'name' => 'User 1'] // Duplicate
        ]);

        // Act
        $result = $this->transformer->transformFilteredEmployees($users);

        // Assert
        $this->assertIsArray($result);
        $this->assertArrayHasKey('filtered_users', $result);
        $this->assertInstanceOf(Collection::class, $result['filtered_users']);
        // Should have unique users
        $this->assertEquals(2, $result['filtered_users']->count());
    }
}
